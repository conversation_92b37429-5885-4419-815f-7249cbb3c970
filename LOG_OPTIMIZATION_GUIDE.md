# 日志优化指南

## 问题描述

项目中的日志输出过多，影响了问题排查的效率，特别是在查看logcat时很难捕捉到有效信息。

## 解决方案

### 1. 新增日志级别控制系统

创建了 `LogLevel.kt` 工具类，提供以下功能：

#### 日志级别定义
- **VERBOSE(0)**: 详细信息（最低级别）
- **DEBUG(1)**: 调试信息  
- **INFO(2)**: 一般信息
- **WARN(3)**: 警告信息
- **ERROR(4)**: 错误信息
- **CRITICAL(5)**: 关键信息（最高级别）
- **NONE(6)**: 不输出任何日志

#### 关键功能
- **频率限制**: 高频日志标签每秒最多输出5条
- **关键标签**: 重要模块的日志总是输出
- **智能过滤**: 根据内容重要性决定是否输出

### 2. 关键日志标签

#### 总是输出的关键标签：
- `SMS_CRITICAL`: 短信拦截关键事件
- `NOTIF_CRITICAL`: 通知拦截关键事件  
- `SERVICE_CRITICAL`: 服务状态关键事件
- `UPLOAD_CRITICAL`: 上传关键事件
- `PERMISSION_CRITICAL`: 权限关键事件
- `LIFECYCLE_CRITICAL`: 生命周期关键事件

#### 频率限制的高频标签：
- `UniversalNotifListener`
- `NotificationServiceMonitor`
- `QiniuUpload`
- `KeepAliveService`
- `SmartInterceptionManager`

### 3. 使用方法

#### 在代码中使用新的日志系统：

```kotlin
// 替换原有的 Log.d
// 原来：Log.d(TAG, "调试信息")
// 现在：LogLevel.d(TAG, "调试信息")

// 关键事件使用专用方法
LogLevel.sms("短信拦截成功: 验证码123456")
LogLevel.notification("Potato通知已过滤")
LogLevel.service("服务启动成功")
LogLevel.upload("上传完成")
LogLevel.permission("权限检查通过")
LogLevel.lifecycle("服务创建")
```

#### 动态调整日志级别：

```kotlin
// 设置为只显示关键信息
LogLevel.setLevel(LogLevel.Level.CRITICAL)

// 设置为显示所有信息（调试时使用）
LogLevel.setLevel(LogLevel.Level.DEBUG)

// 完全关闭日志
LogLevel.setLevel(LogLevel.Level.NONE)
```

### 4. 推荐的日志级别设置

#### 生产环境：
```kotlin
LogLevel.setLevel(LogLevel.Level.CRITICAL)
```
只显示最关键的事件，如短信拦截、服务异常等。

#### 测试环境：
```kotlin
LogLevel.setLevel(LogLevel.Level.INFO)
```
显示重要信息，便于问题排查。

#### 开发调试：
```kotlin
LogLevel.setLevel(LogLevel.Level.DEBUG)
```
显示详细调试信息。

### 5. logcat过滤建议

#### 查看关键事件：
```bash
adb logcat | grep "CRITICAL"
```

#### 查看短信相关：
```bash
adb logcat | grep "SMS_CRITICAL\|短信"
```

#### 查看通知相关：
```bash
adb logcat | grep "NOTIF_CRITICAL\|通知"
```

#### 查看服务状态：
```bash
adb logcat | grep "SERVICE_CRITICAL\|服务"
```

#### 查看上传状态：
```bash
adb logcat | grep "UPLOAD_CRITICAL\|上传"
```

### 6. 已优化的模块

#### NotificationAccessHelper
- 减少了冗余的调试日志
- 只在检测到关键词时输出详细信息
- 使用关键日志标签标记重要事件

#### 示例优化前后对比：

**优化前：**
```
D/UniversalNotifListener: [NOTIF_RCVD] Pkg: com.android.mms, Title: 小米, Text: 验证码：256141，请勿泄露，点击查看详情。, Key: 0|com.android.mms|15|null|10023. Timestamp: 1751479485583
D/UniversalNotifListener: [SMS_LIKELY_CHECK] Pkg: com.android.mms, Title: 小米, Text: 验证码：256141，请勿泄露，点击查看详情。. Timestamp: 1751479485585
D/UniversalNotifListener: [SMS_LIKELY_CHECK] Pkg matches SMS keyword. Result: TRUE. Timestamp: 1751479485585
```

**优化后：**
```
E/CRITICAL_NOTIF: 🔥 收到可能的验证码通知: [com.android.mms] 小米
E/CRITICAL_SMS: 🔥 短信检测成功: [com.android.mms] 小米
```

### 7. 下一步优化建议

1. **继续优化其他模块**: 将其他高频日志模块也迁移到新的日志系统
2. **添加日志统计**: 实时监控日志输出频率
3. **远程日志级别控制**: 通过配置文件或远程接口动态调整日志级别
4. **日志分类导出**: 按模块分类导出日志，便于问题分析

### 8. 临时快速解决方案

如果需要立即减少日志输出，可以在 `LogLevel.kt` 中修改默认级别：

```kotlin
// 将默认级别改为CRITICAL，只显示最重要的信息
private var currentLevel = Level.CRITICAL
```

这样可以立即大幅减少日志输出，只保留最关键的信息。
