# 语音提示和距离控制功能

## 🎙️ 新增功能概览

为人脸识别系统添加了语音提示和距离/姿态控制功能，解决用户姿态不端正和距离不合适的问题。

## 📋 功能详细说明

### 1. TTS语音播报系统

#### **初始化**
- 使用Android原生TTS（Text-to-Speech）
- 优先使用中文，不支持时降级到英文
- 支持语音参数调整（语速、音调）

#### **语音提示类型**

**动作指导语音**
- "请眨眼" - 眨眼动作
- "请张开嘴巴" - 张嘴动作  
- "请点头" - 点头动作
- "请摇头" - 摇头动作
- "请向左转头" - 向左转头
- "请向右转头" - 向右转头

**状态反馈语音**
- "很好，继续下一个动作" - 动作完成时
- "验证完成" - 所有动作完成时
- "请重复动作" - 动作超时时

**距离调整语音**
- "请稍微远离一些" - 距离太近
- "请靠近一些" - 距离太远
- "请调整姿态，露出肩膀" - 需要露出更多身体部分
- "请保持正对摄像头" - 姿态不端正

### 2. 距离检测系统

#### **检测原理**
基于人脸在画面中的宽度比例进行距离判断：

```kotlin
val faceWidthRatio = faceWidth / previewWidth

when {
    faceWidthRatio > 0.70f -> "too_close"     // 太近：>70%
    faceWidthRatio < 0.25f -> "too_far"       // 太远：<25%
    faceWidthRatio < 0.35f -> "show_shoulders" // 建议露出肩膀：<35%
    else -> "good"                            // 合适：25%-70%
}
```

#### **距离标准**
- **太近**：人脸宽度 > 70% 画面宽度
- **太远**：人脸宽度 < 25% 画面宽度
- **建议露出肩膀**：人脸宽度 < 35% 画面宽度
- **合适距离**：人脸宽度在 25%-70% 之间

### 3. 姿态检测系统

#### **检测原理**
基于ML Kit提供的头部欧拉角进行姿态判断：

```kotlin
val maxAngleDeviation = 15f // 允许15度偏差

when {
    abs(headEulerAngleY) > maxAngleDeviation -> "face_straight" // 左右转头过大
    abs(headEulerAngleX) > maxAngleDeviation -> "face_straight" // 点头/抬头过大  
    abs(headEulerAngleZ) > maxAngleDeviation -> "face_straight" // 左右倾斜过大
    else -> "good" // 姿态端正
}
```

#### **姿态标准**
- **X轴角度**：点头/抬头角度 ≤ 15°
- **Y轴角度**：左右转头角度 ≤ 15°
- **Z轴角度**：左右倾斜角度 ≤ 15°

### 4. 实时检测流程

#### **首次人脸检测**
1. 检测到人脸后，先进行距离和姿态检查
2. 只有距离和姿态都合适时，才开始活体检测
3. 不合适时给出语音和文字提示，等待用户调整

#### **活体检测过程中**
1. 在每个活体检测动作执行时，持续检查距离和姿态
2. 发现不合适时暂停检测，给出实时提示
3. 用户调整后自动恢复检测

## 🎯 解决的问题

### **用户姿态问题**
- ✅ 检测头部角度，确保用户正对摄像头
- ✅ 实时语音提示，引导用户调整姿态
- ✅ 只有姿态端正时才开始录制

### **距离控制问题**  
- ✅ 防止用户过于接近摄像头（>70%画面）
- ✅ 防止用户距离过远（<25%画面）
- ✅ 鼓励露出肩膀的专业构图（<35%画面时提示）

### **用户体验问题**
- ✅ 清晰的语音指导，无需看屏幕
- ✅ 实时反馈，及时纠正错误
- ✅ 渐进式引导，从距离到姿态到动作

## 🔧 技术实现

### **核心文件修改**
- `FaceRecognitionDialog.kt` - 主要功能实现

### **新增方法**
- `initTextToSpeech()` - 初始化TTS
- `speakText()` - 语音播报基础方法
- `speakActionGuidance()` - 动作指导语音
- `speakDistanceGuidance()` - 距离调整语音
- `checkFaceDistance()` - 距离检测
- `checkFacePosture()` - 姿态检测

### **集成点**
- 首次人脸检测时的距离姿态检查
- 活体检测过程中的持续监控
- 动作开始时的语音指导
- 动作完成时的成功反馈
- 动作超时时的重复提示

## 📈 预期效果

1. **录制质量提升**：确保用户在合适距离和端正姿态下录制
2. **用户体验改善**：清晰的语音指导减少困惑
3. **专业度提升**：类似证件照的标准构图（露出肩膀）
4. **成功率提高**：减少因姿态或距离问题导致的重录

## 🎵 语音示例

**完整流程语音提示：**
1. "请将脸部对准框内，保持端正姿态"
2. "请稍微远离一些" / "请靠近一些"
3. "请调整姿态，露出肩膀"
4. "请眨眼"
5. "很好，继续下一个动作"
6. "请张开嘴巴"
7. ...
8. "验证完成"

这套系统确保了录制视频的专业性和一致性，解决了用户姿态不端正和距离不合适的核心问题。
