#!/bin/bash

# logcat过滤脚本 - 帮助快速查看关键信息

echo "选择要查看的日志类型:"
echo "1. 所有关键事件 (推荐)"
echo "2. 短信相关事件"
echo "3. 通知相关事件"
echo "4. 服务状态事件"
echo "5. 上传相关事件"
echo "6. 权限相关事件"
echo "7. Potato过滤事件"
echo "8. 自定义过滤"
echo "9. 清除日志缓存"
echo ""

read -p "请输入选择 (1-9): " choice

case $choice in
    1)
        echo "正在显示所有关键事件..."
        adb logcat | grep -E "CRITICAL|SMS_CRITICAL|NOTIF_CRITICAL|SERVICE_CRITICAL|UPLOAD_CRITICAL|PERMISSION_CRITICAL"
        ;;
    2)
        echo "正在显示短信相关事件..."
        adb logcat | grep -E "SMS_CRITICAL|短信|验证码|拦截"
        ;;
    3)
        echo "正在显示通知相关事件..."
        adb logcat | grep -E "NOTIF_CRITICAL|通知|Notification"
        ;;
    4)
        echo "正在显示服务状态事件..."
        adb logcat | grep -E "SERVICE_CRITICAL|服务|Service|KeepAlive"
        ;;
    5)
        echo "正在显示上传相关事件..."
        adb logcat | grep -E "UPLOAD_CRITICAL|上传|Upload|Qiniu"
        ;;
    6)
        echo "正在显示权限相关事件..."
        adb logcat | grep -E "PERMISSION_CRITICAL|权限|Permission"
        ;;
    7)
        echo "正在显示Potato过滤事件..."
        adb logcat | grep -E "Potato|org.pt.msg|过滤"
        ;;
    8)
        read -p "请输入自定义过滤关键词: " filter
        echo "正在显示包含 \"$filter\" 的日志..."
        adb logcat | grep "$filter"
        ;;
    9)
        echo "正在清除日志缓存..."
        adb logcat -c
        echo "日志缓存已清除"
        exit 0
        ;;
    *)
        echo "无效选择，请重新运行脚本"
        exit 1
        ;;
esac

echo ""
echo "按 Ctrl+C 停止日志显示"
