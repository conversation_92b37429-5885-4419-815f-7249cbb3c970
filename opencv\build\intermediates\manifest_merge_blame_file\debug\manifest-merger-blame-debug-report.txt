1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="org.opencv" >
4
5    <uses-sdk android:minSdkVersion="26" />
5-->C:\Users\<USER>\Desktop\5.22MM000000\opencv\src\main\AndroidManifest.xml
6
7    <uses-permission android:name="android.permission.CAMERA" />
7-->C:\Users\<USER>\Desktop\5.22MM000000\opencv\src\main\AndroidManifest.xml:4:5-65
7-->C:\Users\<USER>\Desktop\5.22MM000000\opencv\src\main\AndroidManifest.xml:4:22-62
8    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
8-->C:\Users\<USER>\Desktop\5.22MM000000\opencv\src\main\AndroidManifest.xml:5:5-81
8-->C:\Users\<USER>\Desktop\5.22MM000000\opencv\src\main\AndroidManifest.xml:5:22-78
9    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
9-->C:\Users\<USER>\Desktop\5.22MM000000\opencv\src\main\AndroidManifest.xml:6:5-80
9-->C:\Users\<USER>\Desktop\5.22MM000000\opencv\src\main\AndroidManifest.xml:6:22-77
10
11    <uses-feature
11-->C:\Users\<USER>\Desktop\5.22MM000000\opencv\src\main\AndroidManifest.xml:8:5-84
12        android:name="android.hardware.camera"
12-->C:\Users\<USER>\Desktop\5.22MM000000\opencv\src\main\AndroidManifest.xml:8:19-57
13        android:required="true" />
13-->C:\Users\<USER>\Desktop\5.22MM000000\opencv\src\main\AndroidManifest.xml:8:58-81
14    <uses-feature
14-->C:\Users\<USER>\Desktop\5.22MM000000\opencv\src\main\AndroidManifest.xml:9:5-95
15        android:name="android.hardware.camera.autofocus"
15-->C:\Users\<USER>\Desktop\5.22MM000000\opencv\src\main\AndroidManifest.xml:9:19-67
16        android:required="false" />
16-->C:\Users\<USER>\Desktop\5.22MM000000\opencv\src\main\AndroidManifest.xml:9:68-92
17
18</manifest>
