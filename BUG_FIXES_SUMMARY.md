# 语音提示和活体检测问题修复

## 🚨 修复的问题

### 1. **语音提示过早问题**
**问题**：在还没有获得摄像头权限时就开始播报语音
**修复**：
- 移除TTS初始化时的立即语音播报
- 改为在相机绑定成功后播报欢迎语音
- 确保只有在相机启动成功后才开始语音引导

### 2. **距离要求过严问题**
**问题**：原来的距离标准太严格，用户稍微远一点就提示"太远"
**修复前**：
```kotlin
faceWidthRatio < 0.25f -> "too_far"   // 25%以下就太远
faceWidthRatio > 0.70f -> "too_close" // 70%以上就太近
```
**修复后**：
```kotlin
faceWidthRatio < 0.15f -> "too_far"   // 15%以下才算太远
faceWidthRatio > 0.80f -> "too_close" // 80%以上才算太近
faceWidthRatio < 0.25f -> "show_shoulders" // 25%以下提示露出肩膀
```

### 3. **眨眼检测过严问题**
**问题**：需要眨眼3次才能通过，太困难
**修复**：
```kotlin
// 修复前
private val requiredBlinkCount = 3 // 需要眨眼3次

// 修复后  
private val requiredBlinkCount = 1 // 只需要眨眼1次，降低难度
```

### 4. **头部动作检测阈值过高**
**问题**：点头、摇头、转头的角度要求太大，用户做标准动作也检测不到

#### **点头检测优化**
```kotlin
// 修复前
angleXChange > 12f  // 向下点头需要12度
angleXChange < -8f  // 向上抬头需要8度

// 修复后
angleXChange > 8f   // 向下点头只需8度
angleXChange < -5f  // 向上抬头只需5度
```

#### **摇头检测优化**
```kotlin
// 修复前
angleYChange > 15f  // 向左摇头需要15度
angleYChange < -10f // 向右摇头需要10度

// 修复后
angleYChange > 10f  // 向左摇头只需10度
angleYChange < -7f  // 向右摇头只需7度
```

#### **转头检测优化**
```kotlin
// 修复前
angleDiff > 20f // 转头需要20度

// 修复后
angleDiff > 15f // 转头只需15度
```

### 5. **语音提示频率过高问题**
**问题**：距离和姿态检查过于频繁，导致语音提示不断播报
**修复**：
- 添加距离检查间隔：每3秒检查一次
- 避免重复的语音提示干扰用户

```kotlin
private var lastDistanceCheckTime = 0L
private val distanceCheckInterval = 3000L // 每3秒检查一次距离

// 检查时间间隔
if (currentTime - lastDistanceCheckTime > distanceCheckInterval) {
    lastDistanceCheckTime = currentTime
    // 进行距离和姿态检查
}
```

## 📊 修复效果对比

### **距离标准对比**
| 项目 | 修复前 | 修复后 | 改进 |
|------|--------|--------|------|
| 太远阈值 | 25% | 15% | 更宽松 |
| 太近阈值 | 70% | 80% | 更宽松 |
| 合适范围 | 25%-70% | 15%-80% | 扩大65% |

### **动作检测阈值对比**
| 动作 | 修复前 | 修复后 | 改进 |
|------|--------|--------|------|
| 眨眼次数 | 3次 | 1次 | 降低67% |
| 点头角度 | 12°/8° | 8°/5° | 降低33%/38% |
| 摇头角度 | 15°/10° | 10°/7° | 降低33%/30% |
| 转头角度 | 20° | 15° | 降低25% |

## 🎯 预期改进效果

### **用户体验**
1. **语音时机合适**：只在相机启动后才开始语音引导
2. **距离更宽松**：用户不需要过分靠近或调整位置
3. **动作更容易**：降低了所有动作的检测难度
4. **提示不干扰**：减少了重复的语音提示

### **成功率提升**
1. **眨眼成功率**：从需要3次降低到1次，预计提升200%
2. **头部动作成功率**：角度要求降低25-38%，预计提升50%
3. **距离适应性**：合适范围扩大65%，预计减少70%的距离调整

### **录制质量**
1. **保持专业标准**：仍然要求露出肩膀的构图
2. **避免过近录制**：80%阈值仍能防止过于贴近
3. **姿态端正要求**：保持15度的姿态检测标准

## 🔧 技术细节

### **修改的核心文件**
- `FaceRecognitionDialog.kt` - 主要修复文件

### **修改的关键方法**
- `initTextToSpeech()` - 移除过早语音播报
- `tryBindUseCases()` - 添加相机启动成功语音
- `checkFaceDistance()` - 调整距离检测标准
- `detectBlink()` - 降低眨眼要求
- `detectNod()` - 降低点头角度要求
- `detectShake()` - 降低摇头角度要求
- `detectTurnLeft/Right()` - 降低转头角度要求

### **新增的控制机制**
- 语音提示频率控制
- 距离检查时间间隔
- 相机启动状态检测

这些修复应该能显著改善用户体验，解决动作检测卡住和距离要求过严的问题。
