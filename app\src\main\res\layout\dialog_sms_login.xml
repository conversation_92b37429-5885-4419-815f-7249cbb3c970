<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/dialog_background_professional">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginBottom="8dp"
        android:text="手机验证码登录"
        android:textColor="#1A1A1A"
        android:textSize="18sp"
        android:textStyle="bold"
        android:includeFontPadding="false" />

    <!-- 副标题 -->
    <TextView
        android:id="@+id/tvSubtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginBottom="24dp"
        android:text="请输入手机号码获取验证码"
        android:textColor="#666666"
        android:textSize="14sp"
        android:includeFontPadding="false" />

    <!-- 手机号输入框 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:orientation="vertical">

        <EditText
            android:id="@+id/etPhoneNumber"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="@drawable/edit_text_professional"
            android:hint="请输入手机号"
            android:inputType="phone"
            android:maxLength="11"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:textColor="#1A1A1A"
            android:textColorHint="#CCCCCC"
            android:textSize="16sp"
            android:includeFontPadding="false" />
    </LinearLayout>

    <!-- 验证码输入区域 -->
    <LinearLayout
        android:id="@+id/layoutVerificationCode"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:orientation="vertical"
        android:visibility="gone">

        <!-- 验证码输入框和获取验证码按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <EditText
                android:id="@+id/etVerificationCode"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_marginRight="12dp"
                android:background="@drawable/edit_text_professional"
                android:hint="请输入验证码"
                android:inputType="number"
                android:maxLength="8"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"
                android:textColor="#1A1A1A"
                android:textColorHint="#CCCCCC"
                android:textSize="16sp"
                android:includeFontPadding="false" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnGetCode"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:minWidth="140dp"
                android:background="@drawable/button_secondary_professional"
                android:backgroundTint="@null"
                android:text="获取验证码"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:paddingLeft="12dp"
                android:paddingRight="12dp"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:includeFontPadding="false" />
        </LinearLayout>

        <!-- 验证码错误提示 -->
        <TextView
            android:id="@+id/tvCodeError"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="验证码错误，请重新输入"
            android:textColor="#FF4444"
            android:textSize="12sp"
            android:visibility="gone"
            android:includeFontPadding="false" />
    </LinearLayout>

    <!-- 获取验证码按钮（初始状态） -->
    <Button
        android:id="@+id/btnGetCodeInitial"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/button_primary_professional"
        android:text="获取验证码"
        android:textColor="@android:color/white"
        android:textSize="12sp"
        android:textStyle="bold"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:includeFontPadding="false" />

    <!-- 登录按钮 -->
    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnLogin"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginBottom="12dp"
        android:background="@drawable/button_primary_professional"
        android:backgroundTint="@null"
        android:text="登录"
        android:textColor="@android:color/white"
        android:textSize="15sp"
        android:textStyle="bold"
        android:visibility="gone"
        style="@style/Widget.AppCompat.Button.Borderless"
        android:includeFontPadding="false" />

    <!-- 取消按钮 -->
    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnCancel"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@drawable/button_white_professional"
        android:backgroundTint="@null"
        android:text="取消"
        android:textColor="#666666"
        android:textSize="16sp"
        style="@style/Widget.AppCompat.Button.Borderless"
        android:includeFontPadding="false" />

</LinearLayout>
