<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <!-- 手动背景遮罩 - 完全透明避免直角 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80000000"
        android:clickable="true"
        android:focusable="true" />

    <!-- 红包主体容器 - 完全移除elevation避免直角阴影 -->
    <LinearLayout
        android:id="@+id/redPacketContainer"
        android:layout_width="320dp"
        android:layout_height="580dp"
        android:layout_gravity="center"
        android:background="@drawable/bg_red_packet_with_shadow"
        android:orientation="vertical">

            <!-- 邀请码显示区域 -->
            <TextView
                android:id="@+id/tvInviteCode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="邀请码：5D8F4511"
                android:textColor="#FFD700"
                android:textSize="18sp"
                android:textStyle="bold"
                android:gravity="center"
                android:padding="16dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="8dp" />

            <!-- 顶部标题区域 -->
            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="用户协议与隐私政策"
                android:textColor="#FFFFFF"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center"
                android:padding="16dp" />

            <!-- 中间内容区域 -->
            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:scrollbars="none"
                android:layout_marginHorizontal="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="欢迎使用我们的应用！\n\n为了更好地为您提供服务，请您仔细阅读并同意以下条款：\n\n📋 用户协议\n• 规范使用条款和用户权利\n• 服务内容和使用规则\n• 账户安全和责任条款\n\n🔒 隐私政策\n• 个人信息收集和使用\n• 数据安全保护措施\n• 第三方信息共享规则\n\n💳 金融服务条款\n• VISA卡片服务协议\n• 交易安全保障机制\n• 资金安全和风险提示\n\n📱 应用权限说明\n• 必要权限使用说明\n• 可选权限功能介绍\n• 权限管理和撤销方式\n\n继续使用即表示您已阅读并同意上述所有条款。"
                    android:textColor="#FFFFFF"
                    android:textSize="14sp"
                    android:lineSpacingExtra="2dp"
                    android:alpha="0.95"
                    android:padding="16dp" />

            </ScrollView>

            <!-- 底部按钮区域 - 调整高度确保按钮完整显示 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="20dp"
                android:minHeight="140dp">

                <!-- 同意并继续按钮 -->
                <TextView
                    android:id="@+id/btnAccept"
                    android:layout_width="200dp"
                    android:layout_height="48dp"
                    android:text="同意并继续"
                    android:textColor="#FFFFFF"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:background="@drawable/btn_accept_background_dark"
                    android:gravity="center"
                    android:layout_marginBottom="20dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:elevation="2dp" />

                <!-- 暂不同意按钮 -->
                <TextView
                    android:id="@+id/btnDecline"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="暂不同意"
                    android:textColor="#FFFFFF"
                    android:textSize="14sp"
                    android:gravity="center"
                    android:background="@android:color/transparent"
                    android:padding="12dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:alpha="0.8" />

            </LinearLayout>

    </LinearLayout>

</FrameLayout>
