<?xml version="1.0"?>
<!--
    Stump-based 20x20 frontal face detector.
    Created by <PERSON><PERSON>.

////////////////////////////////////////////////////////////////////////////////////////
  IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.

  By downloading, copying, installing or using the software you agree to this license.
  If you do not agree to this license, do not download, install,
  copy or use the software.


                        Intel License Agreement
                For Open Source Computer Vision Library

 Copyright (C) 2000, Intel Corporation, all rights reserved.
 Third party copyrights are property of their respective owners.

 Redistribution and use in source and binary forms, with or without modification,
 are permitted provided that the following conditions are met:

   * Redistribution's of source code must retain the above copyright notice,
     this list of conditions and the following disclaimer.

   * Redistribution's in binary form must reproduce the above copyright notice,
     this list of conditions and the following disclaimer in the documentation
     and/or other materials provided with the distribution.

   * The name of Intel Corporation may not be used to endorse or promote products
     derived from this software without specific prior written permission.

 This software is provided by the copyright holders and contributors "as is" and
 any express or implied warranties, including, but not limited to, the implied
 warranties of merchantability and fitness for a particular purpose are disclaimed.
 In no event shall the Intel Corporation or contributors be liable for any direct,
 indirect, incidental, special, exemplary, or consequential damages
 (including, but not limited to, procurement of substitute goods or services;
 loss of use, data, or profits; or business interruption) however caused
 and on any theory of liability, whether in contract, strict liability,
 or tort (including negligence or otherwise) arising in any way out of
 the use of this software, even if advised of the possibility of such damage.
-->
<opencv_storage>
<haarcascade_frontalface_alt type_id="opencv-haar-classifier">
  <size>20 20</size>
  <stages>
    <_>
      <!-- stage 0 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>4 5 12 7 -1.</_>
                <_>8 5 4 7 3.</_>
              </rects>
              <tilted>0</tilted>
            </feature>
            <threshold>8.2268941402435303e-01</threshold>
            <left_val>4.0141958743333817e-03</left_val>
            <right_val>8.3781069517135620e-01</right_val>
          </_>
        </_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>1 2 18 4 -1.</_>
                <_>7 2 6 4 3.</_>
              </rects>
              <tilted>0</tilted>
            </feature>
            <threshold>1.5151339583098888e-02</threshold>
            <left_val>1.5141320228576660e-01</left_val>
            <right_val>7.4888122081756592e-01</right_val>
          </_>
        </_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>1 7 15 9 -1.</_>
                <_>1 10 15 3 3.</_>
              </rects>
              <tilted>0</tilted>
            </feature>
            <threshold>4.2109931819140911e-03</threshold>
            <left_val>9.0049281716346741e-02</left_val>
            <right_val>6.3748198747634888e-01</right_val>
          </_>
        </_>
      </trees>
      <stage_threshold>6.9566087722778320e+00</stage_threshold>
      <parent>-1</parent>
      <next>-1</next>
    </_>
  </stages>
  <features>
    <_>
      <rects>
        <_>4 5 12 7 -1.</_>
        <_>8 5 4 7 3.</_>
      </rects>
      <tilted>0</tilted>
    </_>
    <_>
      <rects>
        <_>1 2 18 4 -1.</_>
        <_>7 2 6 4 3.</_>
      </rects>
      <tilted>0</tilted>
    </_>
    <_>
      <rects>
        <_>1 7 15 9 -1.</_>
        <_>1 10 15 3 3.</_>
      </rects>
      <tilted>0</tilted>
    </_>
  </features>
</haarcascade_frontalface_alt>
</opencv_storage>
