<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:duration="500"
    android:interpolator="@android:anim/overshoot_interpolator">

    <!-- 缩放动画：从0.3倍放大到1.0倍 -->
    <scale
        android:fromXScale="0.3"
        android:fromYScale="0.3"
        android:toXScale="1.0"
        android:toYScale="1.0"
        android:pivotX="50%"
        android:pivotY="50%"
        android:duration="500" />

    <!-- 透明度动画：从透明到不透明 -->
    <alpha
        android:fromAlpha="0.0"
        android:toAlpha="1.0"
        android:duration="300" />

    <!-- 旋转动画：轻微旋转增加趣味性 -->
    <rotate
        android:fromDegrees="-10"
        android:toDegrees="0"
        android:pivotX="50%"
        android:pivotY="50%"
        android:duration="500" />

</set>
