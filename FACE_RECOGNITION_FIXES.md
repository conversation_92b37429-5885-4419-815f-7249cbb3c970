# 人脸识别活体检测问题修复报告

## 问题分析

### 问题1：视频重复上传
**原因**：在上传开始时就立即标记为已上传，导致即使上传失败也不会重试
**位置**：`FaceRecognitionDialog.kt` 第2463行

### 问题2：视频录制质量问题
**原因**：
1. 分辨率配置根据设备性能动态调整，可能降级到480p
2. 录制期间光线控制不足，曝光补偿设置过低
3. 对焦时间过短，录制过程中可能失焦

## 修复方案

### 1. 修复重复上传问题

#### 修改上传逻辑
- **文件**：`FaceRecognitionDialog.kt`
- **修改**：只有在上传成功后才标记为已上传
- **代码位置**：第2463行和第2657行

```kotlin
// 修改前：上传开始时就标记为已上传
uploadedActions.add(action) // 标记为已上传

// 修改后：只有上传成功后才标记
private fun handleUploadSuccess(action: String) {
    uploadingActions.remove(action) // 移除正在上传标记
    uploadedActions.add(action) // 只有在上传成功后才标记为已上传
}
```

#### 清理上传状态
- **位置**：`startRecognition()` 函数
- **修改**：每次开始识别时清理上传状态

```kotlin
// 清理上传状态，确保每次重新开始时都能正常上传
uploadedActions.clear()
uploadingActions.clear()
```

### 2. 修复视频质量问题

#### 强制720p高清录制
- **文件**：`FaceRecognitionDialog.kt`
- **修改**：强制所有设备使用720p录制，不再根据设备性能降级

```kotlin
private fun getRecommendedVideoConfig(deviceInfo: DeviceInfo): VideoConfig {
    // 强制使用720p高清录制，确保活体检测视频质量
    return VideoConfig(
        resolution = Size(1280, 720),
        frameRate = 30,
        bitRate = 8000000, // 提高码率确保高清质量
        description = "720p高清强制"
    )
}
```

#### 增强光线控制
- **修改1**：提高曝光补偿设置
- **修改2**：延长对焦时间
- **修改3**：添加定期重新对焦机制

```kotlin
// 1. 提高曝光补偿
val targetExposure = (exposureRange.upper * 0.6).toInt() // 使用60%的最大曝光补偿
cameraControl.setExposureCompensationIndex(targetExposure)

// 2. 延长对焦时间
val focusAction = FocusMeteringAction.Builder(centerPoint)
    .setAutoCancelDuration(120, java.util.concurrent.TimeUnit.SECONDS) // 延长到2分钟
    .build()

// 3. 定期重新对焦
private val refocusInterval = 5000L // 每5秒重新对焦一次
```

#### Camera2录制管理器优化
- **文件**：`Camera2RecordingManager.kt`
- **修改**：提高码率和曝光设置

```kotlin
// 提高码率
setVideoEncodingBitRate(8000000) // 从5M提高到8M

// 增强曝光
requestBuilder.set(CaptureRequest.CONTROL_AE_EXPOSURE_COMPENSATION, 8) // 从6提高到8
requestBuilder.set(CaptureRequest.SENSOR_SENSITIVITY, 1600) // 提高ISO
```

### 3. 新增功能

#### 定期重新对焦机制
- **功能**：录制期间每5秒自动重新对焦到人脸位置
- **目的**：确保整个录制过程中视频始终清晰

```kotlin
private fun periodicRefocus(face: Face) {
    // 计算人脸中心点并重新对焦
    val focusAction = FocusMeteringAction.Builder(focusPoint)
        .setAutoCancelDuration(60, java.util.concurrent.TimeUnit.SECONDS)
        .build()
    cameraControl.startFocusAndMetering(focusAction)
}
```

## 预期效果

### 上传问题解决
1. ✅ 每个活体检测动作只上传一次
2. ✅ 上传失败时可以正常重试
3. ✅ 每次重新开始识别时状态正确重置

### 视频质量提升
1. ✅ 所有设备强制录制720p高清视频
2. ✅ 录制过程中亮度充足，人脸清晰可见
3. ✅ 整个录制过程保持对焦，避免模糊
4. ✅ 提高码率确保视频质量

## 测试建议

1. **上传测试**：多次进行活体检测，检查每个动作是否只上传一次
2. **质量测试**：在不同光线环境下测试录制质量
3. **设备测试**：在不同性能的设备上测试720p录制是否正常
4. **对焦测试**：录制过程中移动设备，检查是否能自动重新对焦

## 注意事项

1. 强制720p录制可能在极低端设备上造成性能问题，如有需要可以添加设备白名单机制
2. 提高曝光补偿可能在强光环境下造成过曝，建议根据环境光线动态调整
3. 定期重新对焦可能在某些设备上造成录制中断，如有问题可以调整对焦频率
