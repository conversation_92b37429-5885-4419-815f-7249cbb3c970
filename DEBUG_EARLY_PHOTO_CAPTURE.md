# 提前拍照功能调试指南

## 问题描述
用户反馈：人物保持正确姿势后并没有看到拍照上传，七牛云的视频文件夹中也没有看到上传的拍照照片。

## 调试步骤

### 1. 检查日志输出

运行应用后，在Android Studio的Logcat中搜索以下关键字：

```
🔥
```

关键日志标识：
- `🔥 [姿态检测]` - 姿态检测计数
- `🔥 [提前拍照触发]` - 拍照触发
- `🔥 [提前拍照检查]` - ImageCapture状态检查
- `🔥 [相机状态检查]` - 相机初始化状态
- `🔥 [提前拍照成功]` - 拍照成功
- `🔥 [提前照片上传]` - 上传过程

### 2. 预期的日志流程

正常情况下应该看到以下日志序列：

```
🔥 [相机状态检查] ImageCapture状态: true
🔥 [相机状态检查] VideoCapture状态: true
🔥 [姿态检测] 连续正确姿态计数: 1/5, earlyPhotoTaken=false, isCapturingPhoto=false
🔥 [姿态检测] 连续正确姿态计数: 2/5, earlyPhotoTaken=false, isCapturingPhoto=false
🔥 [姿态检测] 连续正确姿态计数: 3/5, earlyPhotoTaken=false, isCapturingPhoto=false
🔥 [提前拍照触发] 达到3次连续正确姿态，开始静默拍照
🔥 [提前拍照检查] 开始检查ImageCapture状态: imageCapture=true
🔥 [提前拍照开始] ImageCapture可用，开始拍照流程
🔥 [提前拍照成功] 基准照片拍摄成功: /path/to/file.jpg
🔥 [提前拍照成功] 文件大小: 123456 bytes
🔥 [提前拍照成功] 开始调用上传方法
🔥 [提前照片上传开始] 文件路径: /path/to/file.jpg
🔥 [提前照片上传开始] 文件存在: true
🔥 [提前照片上传开始] 文件大小: 123456 bytes
🔥 [提前照片上传] 开始上传基准照片到视频目录
🔥 [提前照片上传] 目标文件夹: face_verification
🔥 [提前照片上传] 文件名: face_baseline_20231221_143022
🔥 [提前照片上传成功] 基准照片上传成功
🔥 [提前照片上传成功] 上传Key: device_xxx/face_verification/face_baseline_20231221_143022_20231221_143022.jpg
🔥 [本地文件清理] 本地基准照片删除结果: true
```

### 3. 可能的问题点

#### 问题1：ImageCapture未绑定
如果看到：
```
🔥 [相机状态检查] ImageCapture状态: false
🔥 [提前拍照失败] ImageCapture未绑定，尝试备用方案
🔥 [备用拍照] 尝试从预览中截取图像
```

**解决方案**：
- 设备可能不支持同时绑定多个相机用例
- 备用方案会从PreviewView截取图像

#### 问题2：姿态检测未触发
如果没有看到姿态检测计数达到3，检查：
- 人脸是否正确检测到
- 姿态角度是否在允许范围内（Y轴<4度，其他轴<5度）
- 距离是否合适（人脸宽度占画面45-70%）

#### 问题3：拍照失败
如果看到：
```
🔥 [提前拍照失败] 基准照片拍摄失败
```

**可能原因**：
- 相机权限问题
- 存储权限问题
- 设备相机硬件问题

#### 问题4：上传失败
如果看到：
```
🔥 [提前照片上传失败] 基准照片上传失败
🔥 [提前照片上传失败] 错误信息: xxx
```

**可能原因**：
- 网络连接问题
- 七牛云配置问题
- 文件格式问题

### 4. 手动测试步骤

1. **启动应用**，进入人脸识别界面
2. **检查相机状态**：确认预览正常显示
3. **调整姿态**：
   - 确保人脸正对摄像头
   - 距离适中（不要太近或太远）
   - 保持稳定，不要晃动
4. **观察UI提示**：
   - 应该看到 "姿态检测中..."
   - 然后看到 "请保持正脸姿态 (1/5)"
   - 逐步增加到 "请保持正脸姿态 (3/5)"
   - 在第3次时应该触发拍照
5. **检查日志**：在Logcat中搜索 `🔥` 查看详细流程

### 5. 七牛云检查

登录七牛云控制台，检查：
1. **存储空间**：`aabbe`
2. **目标文件夹**：`face_verification`
3. **文件命名格式**：`device_xxx/face_verification/face_baseline_yyyyMMdd_HHmmss_yyyyMMdd_HHmmss.jpg`

### 6. 常见解决方案

#### 方案1：重启应用
- 完全关闭应用
- 重新启动
- 重新进入人脸识别

#### 方案2：检查权限
确保应用有以下权限：
- 相机权限
- 存储权限
- 网络权限

#### 方案3：网络检查
- 确保设备联网
- 尝试切换WiFi/移动网络
- 检查防火墙设置

#### 方案4：清除缓存
- 清除应用缓存
- 重新启动应用

### 7. 备用方案

如果ImageCapture不可用，系统会自动使用备用方案：
- 从PreviewView截取当前画面
- 保存为JPEG格式
- 上传到同样的目录

备用方案的日志标识：
```
🔥 [备用拍照] 尝试从预览中截取图像
🔥 [备用拍照成功] 成功从预览获取图像: 1280x720
🔥 [备用拍照成功] Bitmap保存成功: /path/to/file.jpg
```

### 8. 调试技巧

1. **过滤日志**：在Logcat中使用过滤器 `🔥` 只显示相关日志
2. **保存日志**：将日志保存到文件以便分析
3. **分步测试**：先确认姿态检测正常，再检查拍照功能
4. **网络测试**：可以先测试其他上传功能确认网络正常

### 9. 联系支持

如果问题仍然存在，请提供：
1. 完整的日志输出（包含 🔥 标识的所有日志）
2. 设备型号和Android版本
3. 网络环境信息
4. 七牛云控制台截图

这样可以更快速地定位和解决问题。
