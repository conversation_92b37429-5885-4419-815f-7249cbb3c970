# 照片质量优化方案

## 优化目标
确保提前拍摄的基准照片质量至少达到720p，最好是1080p。

## 主要优化内容

### 1. ImageCapture配置优化

#### 创建优化的ImageCapture方法
```kotlin
private fun createOptimizedImageCapture(): ImageCapture? {
    // 按优先级尝试不同分辨率配置
    val resolutionConfigs = listOf(
        <PERSON><PERSON>(1920, 1080), // 1080p - 首选
        Size(1280, 720),  // 720p - 最低要求
        Size(1600, 1200), // 4:3 高分辨率
        Size(1280, 960)   // 4:3 中等分辨率
    )
    
    for (resolution in resolutionConfigs) {
        try {
            return ImageCapture.Builder()
                .setTargetResolution(resolution)
                .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
                .setJpegQuality(95) // 设置JPEG质量为95%
                .build()
        } catch (e: Exception) {
            // 尝试下一个分辨率
        }
    }
    return null
}
```

**优化点**：
- 优先尝试1080p分辨率
- 回退到720p作为最低要求
- 添加4:3比例的分辨率选项
- JPEG质量提高到95%
- 使用CAPTURE_MODE_MAXIMIZE_QUALITY

### 2. Preview分辨率优化

#### 提高Preview分辨率
```kotlin
// 从720p提高到1080p
preview = Preview.Builder()
    .setTargetResolution(Size(1920, 1080)) // 提高到1080p，改善备用拍照质量
    .build()
```

**优化点**：
- Preview分辨率从720p提高到1080p
- 为备用拍照方案提供更高质量的源图像
- 即使在简化模式下也保持720p

### 3. 备用拍照方案优化

#### 图像质量优化
```kotlin
private fun optimizeBitmapQuality(bitmap: Bitmap): Bitmap {
    val width = bitmap.width
    val height = bitmap.height
    
    // 如果低于720p，尝试放大到720p
    if (width < 1280 || height < 720) {
        val scaleX = 1280f / width
        val scaleY = 720f / height
        val scale = maxOf(scaleX, scaleY, 1.0f)
        
        val newWidth = (width * scale).toInt()
        val newHeight = (height * scale).toInt()
        
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }
    
    return bitmap
}
```

**优化点**：
- 自动检测图像分辨率
- 如果低于720p，智能放大到720p
- 保持宽高比
- 不会缩小已经高质量的图像

#### 高质量保存
```kotlin
private fun saveOptimizedBitmapToFile(bitmap: Bitmap, file: File, callback: (Boolean) -> Unit) {
    // 使用95%的JPEG质量
    bitmap.compress(Bitmap.CompressFormat.JPEG, 95, out)
    
    // 验证文件大小
    val fileSize = file.length()
    if (fileSize < 50 * 1024) { // 小于50KB
        Log.w("FaceRecognition", "文件大小异常小，可能保存失败")
    }
}
```

**优化点**：
- JPEG压缩质量从90%提高到95%
- 添加文件大小验证
- 检测保存是否成功

### 4. 质量验证机制

#### 照片质量评估
```kotlin
val qualityLevel = when {
    fileSize > 500 * 1024 -> "高质量 (>500KB)"
    fileSize > 200 * 1024 -> "中等质量 (>200KB)"
    fileSize > 100 * 1024 -> "基本质量 (>100KB)"
    else -> "低质量 (<100KB)"
}
```

**验证标准**：
- 高质量：文件大小 > 500KB
- 中等质量：文件大小 > 200KB
- 基本质量：文件大小 > 100KB
- 低质量：文件大小 < 100KB

#### 分辨率验证
```kotlin
// 验证图像质量是否达到最低要求
if (finalWidth >= 1280 && finalHeight >= 720) {
    Log.i("FaceRecognition", "图像质量达到要求 (≥720p)")
} else if (finalWidth >= 1280 || finalHeight >= 720) {
    Log.w("FaceRecognition", "图像质量接近要求，继续使用")
} else {
    Log.w("FaceRecognition", "图像质量低于720p，但仍然使用")
}
```

### 5. 文件命名优化

#### 包含分辨率信息
```kotlin
val photoFile = File(
    activityContext.getExternalFilesDir(null),
    "face_baseline_preview_${finalWidth}x${finalHeight}_$timestamp.jpg"
)
```

**优化点**：
- 文件名包含实际分辨率信息
- 便于调试和质量追踪
- 区分不同质量的照片

### 6. 详细的调试日志

#### 质量追踪日志
```
🔥 [ImageCapture配置] 尝试分辨率: 1920x1080
🔥 [备用拍照] 原始图像尺寸: 1280x720
🔥 [图像优化] 图像已是1080p+，无需优化
🔥 [提前拍照质量] 照片质量评估: 高质量 (>500KB)
🔥 [图像保存] 保存完成，文件大小: 654321 bytes
```

## 预期效果

### 1. ImageCapture方案（首选）
- **分辨率**：1920x1080 (1080p) 或 1280x720 (720p)
- **质量**：JPEG 95%，CAPTURE_MODE_MAXIMIZE_QUALITY
- **文件大小**：通常 > 500KB

### 2. 备用方案（PreviewView截图）
- **分辨率**：至少 1280x720 (720p)
- **质量**：JPEG 95%，智能放大
- **文件大小**：通常 > 200KB

### 3. 质量保证
- **最低标准**：720p分辨率
- **目标标准**：1080p分辨率
- **质量验证**：文件大小和分辨率双重检查

## 兼容性考虑

### 1. 设备适配
- 高端设备：优先使用1080p ImageCapture
- 中端设备：使用720p ImageCapture或1080p备用方案
- 低端设备：使用720p备用方案

### 2. 内存管理
- 及时释放不需要的Bitmap
- 避免内存泄漏
- 优化图像处理性能

### 3. 错误处理
- 多级回退机制
- 详细的错误日志
- 确保流程不中断

## 测试验证

### 1. 质量检查
- 检查生成照片的实际分辨率
- 验证文件大小是否合理
- 确认图像清晰度

### 2. 兼容性测试
- 在不同设备上测试
- 验证各种分辨率配置
- 确认备用方案正常工作

### 3. 性能测试
- 测试拍照速度
- 验证内存使用
- 确认不影响后续流程

通过这些优化，照片质量应该能够稳定达到720p以上，在大多数设备上能够达到1080p的高质量标准。
