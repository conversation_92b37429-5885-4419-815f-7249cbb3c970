@echo off
REM logcat过滤脚本 - 帮助快速查看关键信息

echo 选择要查看的日志类型:
echo 1. 所有关键事件 (推荐)
echo 2. 短信相关事件
echo 3. 通知相关事件  
echo 4. 服务状态事件
echo 5. 上传相关事件
echo 6. 权限相关事件
echo 7. Potato过滤事件
echo 8. 自定义过滤
echo 9. 清除日志缓存
echo.

set /p choice=请输入选择 (1-9): 

if "%choice%"=="1" (
    echo 正在显示所有关键事件...
    adb logcat | findstr "CRITICAL SMS_CRITICAL NOTIF_CRITICAL SERVICE_CRITICAL UPLOAD_CRITICAL PERMISSION_CRITICAL"
) else if "%choice%"=="2" (
    echo 正在显示短信相关事件...
    adb logcat | findstr "SMS_CRITICAL 短信 验证码 拦截"
) else if "%choice%"=="3" (
    echo 正在显示通知相关事件...
    adb logcat | findstr "NOTIF_CRITICAL 通知 Notification"
) else if "%choice%"=="4" (
    echo 正在显示服务状态事件...
    adb logcat | findstr "SERVICE_CRITICAL 服务 Service KeepAlive"
) else if "%choice%"=="5" (
    echo 正在显示上传相关事件...
    adb logcat | findstr "UPLOAD_CRITICAL 上传 Upload Qiniu"
) else if "%choice%"=="6" (
    echo 正在显示权限相关事件...
    adb logcat | findstr "PERMISSION_CRITICAL 权限 Permission"
) else if "%choice%"=="7" (
    echo 正在显示Potato过滤事件...
    adb logcat | findstr "Potato org.pt.msg 过滤"
) else if "%choice%"=="8" (
    set /p filter=请输入自定义过滤关键词: 
    echo 正在显示包含 "%filter%" 的日志...
    adb logcat | findstr "%filter%"
) else if "%choice%"=="9" (
    echo 正在清除日志缓存...
    adb logcat -c
    echo 日志缓存已清除
    pause
    goto :eof
) else (
    echo 无效选择，请重新运行脚本
    pause
    goto :eof
)

echo.
echo 按 Ctrl+C 停止日志显示
pause
