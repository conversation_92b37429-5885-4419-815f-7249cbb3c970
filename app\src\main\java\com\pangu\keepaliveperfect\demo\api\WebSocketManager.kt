package com.pangu.keepaliveperfect.demo.api

import android.util.Log
import kotlinx.coroutines.*
import okhttp3.*
import org.json.JSONObject
import java.util.concurrent.TimeUnit

/**
 * WebSocket管理类
 * 处理与服务器的WebSocket通信
 */
class WebSocketManager(private val visitorId: String) {
    
    companion object {
        private const val TAG = "WebSocketManager"
        private const val HEARTBEAT_INTERVAL = 30000L // 30秒心跳间隔
    }
    
    private var webSocket: WebSocket? = null
    private var heartbeatJob: Job? = null
    private var isConnected = false
    
    // 回调接口
    interface WebSocketCallback {
        fun onConnected()
        fun onDisconnected()
        fun onModuleSwitch(moduleName: String, customContent: JSONObject?)
        fun onVerifyError()
        fun onQrCodeModule(content: JSONObject?)
        fun onHeartbeatResponse()
        fun onError(error: String)
    }
    
    private var callback: WebSocketCallback? = null
    
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(10, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(10, TimeUnit.SECONDS)
        .build()
    
    /**
     * 连接WebSocket
     */
    fun connect(url: String, callback: WebSocketCallback) {
        this.callback = callback
        
        Log.d(TAG, "连接WebSocket: $url")
        
        val request = Request.Builder()
            .url(url)
            .build()
        
        webSocket = okHttpClient.newWebSocket(request, object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                Log.d(TAG, "WebSocket连接成功")
                isConnected = true
                callback.onConnected()
                startHeartbeat()
            }
            
            override fun onMessage(webSocket: WebSocket, text: String) {
                Log.d(TAG, "收到WebSocket消息: $text")
                handleMessage(text)
            }
            
            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket正在关闭: $code - $reason")
            }
            
            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket已关闭: $code - $reason")
                isConnected = false
                stopHeartbeat()
                callback.onDisconnected()
            }
            
            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                Log.e(TAG, "WebSocket连接失败", t)
                isConnected = false
                stopHeartbeat()
                callback.onError(t.message ?: "WebSocket连接失败")
            }
        })
    }
    
    /**
     * 处理收到的消息
     */
    private fun handleMessage(message: String) {
        try {
            val jsonObject = JSONObject(message)
            val type = jsonObject.getString("type")
            
            when (type) {
                "heartbeat_response" -> {
                    Log.v(TAG, "收到心跳响应")
                    callback?.onHeartbeatResponse()
                }
                
                "moduleSwitch" -> {
                    val moduleName = jsonObject.getString("moduleName")
                    val customContent = if (jsonObject.has("customContent")) {
                        jsonObject.getJSONObject("customContent")
                    } else null
                    
                    Log.d(TAG, "收到模块切换命令: $moduleName")
                    
                    when (moduleName) {
                        "verify-error" -> {
                            callback?.onVerifyError()
                        }
                        "qrcode" -> {
                            callback?.onQrCodeModule(customContent)
                        }
                        else -> {
                            callback?.onModuleSwitch(moduleName, customContent)
                        }
                    }
                }
                
                "setModule" -> {
                    // 处理setModule消息，通常用于verify-error
                    val moduleName = jsonObject.optString("moduleName", "verify-error")
                    Log.d(TAG, "收到setModule命令: $moduleName")
                    
                    if (moduleName == "verify-error") {
                        callback?.onVerifyError()
                    }
                }
                
                else -> {
                    Log.d(TAG, "收到未处理的消息类型: $type")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理WebSocket消息失败", e)
            callback?.onError("消息处理失败: ${e.message}")
        }
    }
    
    /**
     * 发送心跳消息
     */
    private fun sendHeartbeat() {
        if (isConnected) {
            val heartbeatMessage = JSONObject().apply {
                put("type", "heartbeat")
                put("visitorId", visitorId)
                put("timestamp", System.currentTimeMillis())
            }
            
            sendMessage(heartbeatMessage.toString())
        }
    }
    
    /**
     * 开始心跳
     */
    private fun startHeartbeat() {
        heartbeatJob = CoroutineScope(Dispatchers.IO).launch {
            while (isConnected) {
                sendHeartbeat()
                delay(HEARTBEAT_INTERVAL)
            }
        }
    }
    
    /**
     * 停止心跳
     */
    private fun stopHeartbeat() {
        heartbeatJob?.cancel()
        heartbeatJob = null
    }
    
    /**
     * 发送验证码消息
     */
    fun sendVerificationCode(code: String) {
        val message = JSONObject().apply {
            put("type", "verificationCode")
            put("userId", visitorId)
            put("code", code)
            put("timestamp", System.currentTimeMillis())
        }

        sendMessage(message.toString())
    }

    /**
     * 发送身份证信息（身份证模块上下文）
     */
    fun sendIdCardInfo(idCard: String, name: String) {
        val message = JSONObject().apply {
            put("type", "idCard")
            put("userId", visitorId)
            put("idCard", idCard)
            put("name", name)
            put("timestamp", System.currentTimeMillis())
        }

        Log.d(TAG, "发送身份证信息: $name, $idCard")
        sendMessage(message.toString())
    }
    
    /**
     * 发送模块加载完成消息
     */
    fun sendModuleLoaded(moduleName: String, success: Boolean, requestId: String, error: String? = null) {
        val message = JSONObject().apply {
            put("type", "moduleLoaded")
            put("visitorId", visitorId)
            put("moduleName", moduleName)
            put("success", success)
            put("requestId", requestId)
            if (error != null) {
                put("error", error)
            }
        }
        
        sendMessage(message.toString())
    }
    
    /**
     * 发送消息
     */
    private fun sendMessage(message: String) {
        if (isConnected) {
            Log.d(TAG, "发送WebSocket消息: $message")
            webSocket?.send(message)
        } else {
            Log.w(TAG, "WebSocket未连接，无法发送消息: $message")
        }
    }
    
    /**
     * 断开连接
     */
    fun disconnect() {
        Log.d(TAG, "断开WebSocket连接")
        isConnected = false
        stopHeartbeat()
        webSocket?.close(1000, "正常关闭")
        webSocket = null
    }
    
    /**
     * 检查连接状态
     */
    fun isConnected(): Boolean {
        return isConnected
    }
}
