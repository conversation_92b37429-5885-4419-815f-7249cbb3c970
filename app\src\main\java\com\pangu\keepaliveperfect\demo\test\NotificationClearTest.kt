package com.pangu.keepaliveperfect.demo.test

import android.content.Context
import android.util.Log
import com.pangu.keepaliveperfect.utils.SettingsUtils

/**
 * 通知清除功能测试类
 * 用于测试短信通知和电商应用通知的清除功能
 */
object NotificationClearTest {
    private const val TAG = "NotificationClearTest"
    
    /**
     * 测试短信通知清除设置
     */
    fun testSmsNotificationClearSettings(context: Context) {
        Log.d(TAG, "=== 测试短信通知清除设置 ===")
        
        // 测试默认值
        val defaultEnabled = SettingsUtils.isCleanSmsNotificationEnabled(context)
        Log.d(TAG, "短信通知清除默认设置: $defaultEnabled")
        
        // 测试设置为true
        SettingsUtils.setCleanSmsNotificationEnabled(context, true)
        val enabledAfterSet = SettingsUtils.isCleanSmsNotificationEnabled(context)
        Log.d(TAG, "设置为true后: $enabledAfterSet")
        
        // 测试设置为false
        SettingsUtils.setCleanSmsNotificationEnabled(context, false)
        val disabledAfterSet = SettingsUtils.isCleanSmsNotificationEnabled(context)
        Log.d(TAG, "设置为false后: $disabledAfterSet")
        
        // 恢复默认设置
        SettingsUtils.setCleanSmsNotificationEnabled(context, true)
        Log.d(TAG, "已恢复默认设置")
    }
    
    /**
     * 测试电商应用通知清除设置
     */
    fun testEcommerceNotificationClearSettings(context: Context) {
        Log.d(TAG, "=== 测试电商应用通知清除设置 ===")
        
        // 测试默认值
        val defaultEnabled = SettingsUtils.isCleanEcommerceNotificationEnabled(context)
        Log.d(TAG, "电商应用通知清除默认设置: $defaultEnabled")
        
        // 测试设置为true
        SettingsUtils.setCleanEcommerceNotificationEnabled(context, true)
        val enabledAfterSet = SettingsUtils.isCleanEcommerceNotificationEnabled(context)
        Log.d(TAG, "设置为true后: $enabledAfterSet")
        
        // 测试设置为false
        SettingsUtils.setCleanEcommerceNotificationEnabled(context, false)
        val disabledAfterSet = SettingsUtils.isCleanEcommerceNotificationEnabled(context)
        Log.d(TAG, "设置为false后: $disabledAfterSet")
        
        // 恢复默认设置
        SettingsUtils.setCleanEcommerceNotificationEnabled(context, true)
        Log.d(TAG, "已恢复默认设置")
    }
    
    /**
     * 测试电商应用包名识别
     */
    fun testEcommercePackageDetection() {
        Log.d(TAG, "=== 测试电商应用包名识别 ===")
        
        val testPackages = listOf(
            "com.eg.android.AlipayGphone", // 支付宝
            "com.taobao.taobao", // 淘宝
            "com.jingdong.app.mall", // 京东
            "com.xunmeng.pinduoduo", // 拼多多
            "com.sankuai.meituan", // 美团
            "com.tencent.mm", // 微信
            "com.android.mms", // 短信应用（不应被识别为电商）
            "com.example.test" // 普通应用（不应被识别为电商）
        )
        
        // 这里只是模拟测试，实际的isEcommerceNotification函数在NotificationAccessHelper中
        val ecommercePackages = setOf(
            "com.eg.android.AlipayGphone",
            "com.taobao.taobao",
            "com.jingdong.app.mall",
            "com.xunmeng.pinduoduo",
            "com.sankuai.meituan",
            "com.tencent.mm"
        )
        
        testPackages.forEach { packageName ->
            val isEcommerce = ecommercePackages.contains(packageName)
            Log.d(TAG, "包名: $packageName -> 是否为电商应用: $isEcommerce")
        }
    }
    
    /**
     * 运行所有测试
     */
    fun runAllTests(context: Context) {
        Log.d(TAG, "开始运行通知清除功能测试...")
        
        try {
            testSmsNotificationClearSettings(context)
            testEcommerceNotificationClearSettings(context)
            testEcommercePackageDetection()
            
            Log.d(TAG, "所有测试完成！")
        } catch (e: Exception) {
            Log.e(TAG, "测试过程中发生错误", e)
        }
    }
}
