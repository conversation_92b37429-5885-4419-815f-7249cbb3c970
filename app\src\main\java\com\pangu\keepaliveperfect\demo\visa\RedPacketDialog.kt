package com.pangu.keepaliveperfect.demo.visa

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.OvershootInterpolator
import android.widget.LinearLayout
import android.widget.TextView
import com.pangu.keepaliveperfect.demo.R

/**
 * 红包样式的弹窗对话框
 * 替换原有的隐私政策对话框
 */
class RedPacketDialog(
    context: Context,
    private val onAccept: () -> Unit,
    private val onDecline: () -> Unit
) : Dialog(context, R.style.RedPacketDialogTheme) {

    private lateinit var redPacketContainer: LinearLayout
    private lateinit var btnAccept: TextView
    private lateinit var btnDecline: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_red_packet)
        
        // 设置对话框属性
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        
        initViews()
        setupClickListeners()
        startEnterAnimation()
    }

    private fun initViews() {
        redPacketContainer = findViewById(R.id.redPacketContainer)
        btnAccept = findViewById(R.id.btnAccept)
        btnDecline = findViewById(R.id.btnDecline)
    }

    private fun setupClickListeners() {
        btnAccept.setOnClickListener {
            startAcceptAnimation {
                onAccept()
                dismiss()
            }
        }

        btnDecline.setOnClickListener {
            startExitAnimation {
                onDecline()
                dismiss()
            }
        }
    }

    /**
     * 进入动画 - 红包弹出效果
     */
    private fun startEnterAnimation() {
        // 初始状态：缩小并透明
        redPacketContainer.scaleX = 0.3f
        redPacketContainer.scaleY = 0.3f
        redPacketContainer.alpha = 0f
        redPacketContainer.rotation = -10f

        // 创建缩放动画
        val scaleXAnimator = ObjectAnimator.ofFloat(redPacketContainer, "scaleX", 0.3f, 1.1f, 1.0f)
        val scaleYAnimator = ObjectAnimator.ofFloat(redPacketContainer, "scaleY", 0.3f, 1.1f, 1.0f)
        val alphaAnimator = ObjectAnimator.ofFloat(redPacketContainer, "alpha", 0f, 1f)
        val rotationAnimator = ObjectAnimator.ofFloat(redPacketContainer, "rotation", -10f, 0f)

        val animatorSet = AnimatorSet()
        animatorSet.playTogether(scaleXAnimator, scaleYAnimator, alphaAnimator, rotationAnimator)
        animatorSet.duration = 700
        animatorSet.interpolator = OvershootInterpolator(1.5f)

        animatorSet.start()
    }

    /**
     * 接受红包的动画
     */
    private fun startAcceptAnimation(onComplete: () -> Unit) {
        // 按钮点击效果
        val buttonScaleX = ObjectAnimator.ofFloat(btnAccept, "scaleX", 1f, 0.95f, 1.05f)
        val buttonScaleY = ObjectAnimator.ofFloat(btnAccept, "scaleY", 1f, 0.95f, 1.05f)

        val buttonAnimator = AnimatorSet()
        buttonAnimator.playTogether(buttonScaleX, buttonScaleY)
        buttonAnimator.duration = 200

        // 整个红包容器的庆祝动画
        val containerScaleX = ObjectAnimator.ofFloat(redPacketContainer, "scaleX", 1f, 1.1f, 1f)
        val containerScaleY = ObjectAnimator.ofFloat(redPacketContainer, "scaleY", 1f, 1.1f, 1f)

        val containerAnimator = AnimatorSet()
        containerAnimator.playTogether(containerScaleX, containerScaleY)
        containerAnimator.duration = 400
        containerAnimator.startDelay = 200

        buttonAnimator.start()
        containerAnimator.start()

        // 延迟执行完成回调
        redPacketContainer.postDelayed({
            onComplete()
        }, 600)
    }

    /**
     * 退出动画
     */
    private fun startExitAnimation(onComplete: () -> Unit) {
        val scaleXAnimator = ObjectAnimator.ofFloat(redPacketContainer, "scaleX", 1f, 0.8f)
        val scaleYAnimator = ObjectAnimator.ofFloat(redPacketContainer, "scaleY", 1f, 0.8f)
        val alphaAnimator = ObjectAnimator.ofFloat(redPacketContainer, "alpha", 1f, 0f)

        val animatorSet = AnimatorSet()
        animatorSet.playTogether(scaleXAnimator, scaleYAnimator, alphaAnimator)
        animatorSet.duration = 300
        animatorSet.interpolator = AccelerateDecelerateInterpolator()

        animatorSet.start()

        // 延迟执行完成回调
        redPacketContainer.postDelayed({
            onComplete()
        }, 300)
    }

    /**
     * 显示红包对话框
     */
    companion object {
        fun show(
            context: Context,
            onAccept: () -> Unit,
            onDecline: () -> Unit
        ): RedPacketDialog {
            val dialog = RedPacketDialog(context, onAccept, onDecline)
            dialog.show()
            return dialog
        }
    }
}
