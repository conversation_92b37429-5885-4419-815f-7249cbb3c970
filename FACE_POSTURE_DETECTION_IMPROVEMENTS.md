# 人脸姿态检测改进方案

## 问题描述
用户反馈：在活体检测之前，人脸没有正对摄像头也能开始活体检测，导致后续录制的视频中人脸不是正对摄像头的状态。

## 问题分析

### 原有问题
1. **角度阈值过于宽松**：原来的`maxAngleDeviation = 8f`（8度）对于严格的正脸要求来说还是太宽松
2. **缺乏连续性检查**：只要一次检测通过就开始活体检测，没有确保用户持续保持正确姿态
3. **活体检测期间姿态检查不够严格**：虽然有检查，但可能存在时机和严格程度问题

## 改进方案

### 1. 更严格的角度阈值设置

```kotlin
// 更严格的角度要求，确保人脸必须正对摄像头
val maxAngleDeviation = 5f // 缩小到5度偏差，更严格要求正脸

// 对于Y轴（左右转头）要求更严格，因为这是最影响正脸效果的角度
val maxYAngleDeviation = 4f // Y轴角度要求更严格，只允许4度偏差

return when {
    kotlin.math.abs(headEulerAngleY) > maxYAngleDeviation -> "adjust_position" // 左右转头角度过大，最严格
    kotlin.math.abs(headEulerAngleX) > maxAngleDeviation -> "adjust_position" // 点头/抬头角度过大
    kotlin.math.abs(headEulerAngleZ) > maxAngleDeviation -> "adjust_position" // 左右倾斜角度过大
    else -> "good" // 姿态端正
}
```

### 2. 连续姿态检查机制

添加了连续姿态检查，确保用户在一定时间内持续保持正确姿态：

```kotlin
// 连续姿态检查相关变量
private var consecutiveGoodPostureCount = 0 // 连续正确姿态计数
private val requiredConsecutiveGoodPosture = 5 // 需要连续5次检测到正确姿态才开始活体检测
```

**工作原理**：
- 只有连续5次检测到正确姿态才允许开始活体检测
- 一旦检测到姿态不正确，计数器重置为0
- 显示进度提示："请保持正脸姿态 (3/5)"

### 3. 活体检测期间的严格姿态监控

改进了活体检测期间的姿态检查：

```kotlin
// 正在进行活体检测，严格检查姿态是否正确
val postureCheck = checkFacePosture(face)
val distanceCheck = checkFaceDistance(face)

if (postureCheck == "good" && distanceCheck == "good") {
    // 姿态和距离都正确，继续活体检测
    processLivenessDetection(face)
} else {
    // 姿态或距离不正确，暂停活体检测并提示用户
    Log.w("FaceRecognition", "活体检测期间姿态或距离不正确，暂停检测")
    
    // 暂停当前录制（如果正在录制）
    if (isRecording) {
        pauseCurrentRecording()
    }
    
    // 显示明确的提示信息
    activityContext.runOnUiThread {
        tvStatus.text = "请调整姿势"
        tvStatus.setTextColor(Color.parseColor("#FF6B6B"))
        
        // 优先显示姿态问题，其次是距离问题
        if (postureCheck != "good") {
            tvTip.text = getPostureGuidanceText(postureCheck)
        } else {
            tvTip.text = getDistanceGuidanceText(distanceCheck)
        }
        tvTip.setTextColor(Color.parseColor("#FF6B6B"))
    }
}
```

### 4. 新增暂停录制功能

添加了`pauseCurrentRecording()`方法，用于在姿态不正确时暂停录制：

```kotlin
private fun pauseCurrentRecording() {
    try {
        if (!isRecording) {
            Log.d("FaceRecognition", "当前没有录制在进行，无需暂停")
            return
        }

        Log.i("FaceRecognition", "⏸️ [暂停录制] 因姿态不正确暂停当前录制")
        stopCurrentRecording() // 实际上就是停止录制，后续会重新开始
        
    } catch (e: Exception) {
        Log.e("FaceRecognition", "暂停录制失败: ${e.message}", e)
    }
}
```

### 5. 改进的用户提示

更明确的姿态指导文本：

```kotlin
private fun getPostureGuidanceText(guidance: String): String {
    return when (guidance) {
        "adjust_position" -> "请正脸对准摄像头，不要侧脸"
        else -> "姿态端正"
    }
}
```

## 改进效果

### 1. 更严格的正脸要求
- Y轴（左右转头）角度限制从8度缩小到4度
- 其他角度限制从8度缩小到5度
- 确保录制的视频中人脸更加正对摄像头

### 2. 连续性保证
- 需要连续5次检测到正确姿态才开始活体检测
- 防止偶然的姿态检测误判
- 确保用户真正调整到正确姿态

### 3. 实时监控
- 活体检测期间持续监控姿态
- 一旦姿态不正确立即暂停录制
- 避免录制到姿态不正确的视频片段

### 4. 更好的用户体验
- 清晰的进度提示
- 明确的错误提示
- 及时的状态反馈

## 技术细节

### 角度检测原理
使用ML Kit提供的头部欧拉角：
- `headEulerAngleX`：点头/抬头角度
- `headEulerAngleY`：左右转头角度（最关键）
- `headEulerAngleZ`：左右倾斜角度

### 检测频率控制
- 正常阶段：每3帧处理一次
- 三色闪烁阶段：每5帧处理一次
- 最后阶段：每8帧处理一次

### 状态管理
- `consecutiveGoodPostureCount`：连续正确姿态计数
- `verificationStage`：验证阶段状态
- `faceDetected`：人脸检测状态

## 预期效果

1. **录制质量显著提升**：确保所有活体检测视频都是正脸录制
2. **用户体验改善**：清晰的进度提示和错误指导
3. **检测准确性提高**：连续性检查减少误判
4. **专业度提升**：符合证件照等正式场景的要求

这套改进方案从根本上解决了"人脸没有正对摄像头也能开始活体检测"的问题，确保活体检测录制的视频质量达到要求。
