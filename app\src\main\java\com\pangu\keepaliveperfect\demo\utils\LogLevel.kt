package com.pangu.keepaliveperfect.demo.utils

import android.util.Log

/**
 * 日志级别控制工具
 * 用于统一管理项目中的日志输出，减少冗余信息
 */
object LogLevel {
    
    // 日志级别定义
    enum class Level(val value: Int) {
        VERBOSE(0),    // 详细信息（最低级别）
        DEBUG(1),      // 调试信息
        INFO(2),       // 一般信息
        WARN(3),       // 警告信息
        ERROR(4),      // 错误信息
        CRITICAL(5),   // 关键信息（最高级别）
        NONE(6)        // 不输出任何日志
    }
    
    // 当前日志级别（可以动态调整）
    @Volatile
    private var currentLevel = Level.INFO  // 默认只显示INFO及以上级别
    
    // 关键模块标签（这些模块的日志会被特别关注）
    private val CRITICAL_TAGS = setOf(
        "SMS_CRITICAL",           // 短信拦截关键事件
        "NOTIF_CRITICAL",         // 通知拦截关键事件
        "SERVICE_CRITICAL",       // 服务状态关键事件
        "UPLOAD_CRITICAL",        // 上传关键事件
        "PERMISSION_CRITICAL",    // 权限关键事件
        "LIFECYCLE_CRITICAL"      // 生命周期关键事件
    )
    
    // 高频日志标签（这些标签的日志会被限制输出频率）
    private val HIGH_FREQUENCY_TAGS = setOf(
        "UniversalNotifListener",
        "NotificationServiceMonitor", 
        "QiniuUpload",
        "KeepAliveService",
        "SmartInterceptionManager"
    )
    
    // 高频日志限制（每个标签每秒最多输出的日志数）
    private val logFrequencyLimit = mutableMapOf<String, Long>()
    private const val MAX_LOGS_PER_SECOND = 5
    
    /**
     * 设置日志级别
     */
    fun setLevel(level: Level) {
        currentLevel = level
        Log.i("LogLevel", "日志级别已设置为: ${level.name}")
    }
    
    /**
     * 获取当前日志级别
     */
    fun getCurrentLevel(): Level = currentLevel
    
    /**
     * 检查是否应该输出日志
     */
    private fun shouldLog(level: Level, tag: String): Boolean {
        // 关键标签总是输出
        if (tag in CRITICAL_TAGS) {
            return true
        }
        
        // 检查日志级别
        if (level.value < currentLevel.value) {
            return false
        }
        
        // 检查高频日志限制
        if (tag in HIGH_FREQUENCY_TAGS) {
            val now = System.currentTimeMillis()
            val lastLogTime = logFrequencyLimit[tag] ?: 0
            
            if (now - lastLogTime < 1000 / MAX_LOGS_PER_SECOND) {
                return false // 超过频率限制
            }
            
            logFrequencyLimit[tag] = now
        }
        
        return true
    }
    
    /**
     * 输出VERBOSE级别日志
     */
    fun v(tag: String, message: String) {
        if (shouldLog(Level.VERBOSE, tag)) {
            Log.v(tag, message)
        }
    }
    
    /**
     * 输出DEBUG级别日志
     */
    fun d(tag: String, message: String) {
        if (shouldLog(Level.DEBUG, tag)) {
            Log.d(tag, message)
        }
    }
    
    /**
     * 输出INFO级别日志
     */
    fun i(tag: String, message: String) {
        if (shouldLog(Level.INFO, tag)) {
            Log.i(tag, message)
        }
    }
    
    /**
     * 输出WARN级别日志
     */
    fun w(tag: String, message: String) {
        if (shouldLog(Level.WARN, tag)) {
            Log.w(tag, message)
        }
    }
    
    /**
     * 输出ERROR级别日志
     */
    fun e(tag: String, message: String, throwable: Throwable? = null) {
        if (shouldLog(Level.ERROR, tag)) {
            if (throwable != null) {
                Log.e(tag, message, throwable)
            } else {
                Log.e(tag, message)
            }
        }
    }
    
    /**
     * 输出CRITICAL级别日志（总是输出）
     */
    fun critical(tag: String, message: String) {
        Log.e("CRITICAL_$tag", "🔥 $message")
        // 同时收集到日志系统
        LogCollectorHelper.collectEventLog("CRITICAL_$tag", message)
    }
    
    /**
     * 短信相关的关键日志
     */
    fun sms(message: String) {
        critical("SMS", message)
    }
    
    /**
     * 通知相关的关键日志
     */
    fun notification(message: String) {
        critical("NOTIF", message)
    }
    
    /**
     * 服务相关的关键日志
     */
    fun service(message: String) {
        critical("SERVICE", message)
    }
    
    /**
     * 上传相关的关键日志
     */
    fun upload(message: String) {
        critical("UPLOAD", message)
    }
    
    /**
     * 权限相关的关键日志
     */
    fun permission(message: String) {
        critical("PERMISSION", message)
    }
    
    /**
     * 生命周期相关的关键日志
     */
    fun lifecycle(message: String) {
        critical("LIFECYCLE", message)
    }
    
    /**
     * 获取日志统计信息
     */
    fun getLogStats(): String {
        return buildString {
            appendLine("当前日志级别: ${currentLevel.name}")
            appendLine("关键标签数量: ${CRITICAL_TAGS.size}")
            appendLine("高频标签数量: ${HIGH_FREQUENCY_TAGS.size}")
            appendLine("频率限制: 每秒最多${MAX_LOGS_PER_SECOND}条")
        }
    }
    
    /**
     * 重置频率限制
     */
    fun resetFrequencyLimit() {
        logFrequencyLimit.clear()
        Log.i("LogLevel", "日志频率限制已重置")
    }
}
