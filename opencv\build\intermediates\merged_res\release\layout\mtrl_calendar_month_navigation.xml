<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2019 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:id="@+id/month_navigation_bar"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:paddingTop="@dimen/mtrl_calendar_navigation_top_padding"
  android:paddingBottom="@dimen/mtrl_calendar_navigation_bottom_padding"
  android:orientation="horizontal">

  <FrameLayout
    android:layout_width="0dp"
    android:layout_height="match_parent"
    android:layout_weight="1">

    <com.google.android.material.button.MaterialButton
      style="?attr/materialCalendarYearNavigationButton"
      android:id="@+id/month_navigation_fragment_toggle"
      android:layout_width="wrap_content"
      android:layout_height="@dimen/mtrl_calendar_navigation_height"
      android:layout_gravity="start"
      android:gravity="center_vertical|start"
      android:insetBottom="0dp"
      android:insetTop="0dp"
      app:icon="@drawable/material_ic_menu_arrow_down_black_24dp"
      app:iconGravity="textEnd"
      app:iconPadding="4dp"/>

  </FrameLayout>

  <com.google.android.material.button.MaterialButton
    style="?attr/materialCalendarMonthNavigationButton"
    android:id="@+id/month_navigation_previous"
    android:layout_width="@dimen/mtrl_min_touch_target_size"
    android:layout_height="@dimen/mtrl_calendar_navigation_height"
    android:contentDescription="@string/mtrl_picker_a11y_prev_month"
    android:gravity="center"
    android:insetBottom="0dp"
    android:insetTop="0dp"
    app:icon="@drawable/material_ic_keyboard_arrow_previous_black_24dp"/>

  <com.google.android.material.button.MaterialButton
    style="?attr/materialCalendarMonthNavigationButton"
    android:id="@+id/month_navigation_next"
    android:layout_width="@dimen/mtrl_min_touch_target_size"
    android:layout_height="@dimen/mtrl_calendar_navigation_height"
    android:contentDescription="@string/mtrl_picker_a11y_next_month"
    android:gravity="center"
    android:insetBottom="0dp"
    android:insetTop="0dp"
    app:icon="@drawable/material_ic_keyboard_arrow_next_black_24dp"/>

</LinearLayout>
