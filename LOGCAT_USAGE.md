# Logcat过滤使用指南

## 快速解决方案

### 1. 使用过滤脚本（推荐）

#### Windows用户：
```bash
# 双击运行或在命令行执行
logcat_filter.bat
```

#### Linux/Mac用户：
```bash
# 添加执行权限
chmod +x logcat_filter.sh

# 运行脚本
./logcat_filter.sh
```

### 2. 手动logcat命令

#### 查看所有关键事件：
```bash
adb logcat | grep -E "CRITICAL|🔥"
```

#### 查看短信相关：
```bash
adb logcat | grep -E "SMS_CRITICAL|短信|验证码"
```

#### 查看Potato过滤：
```bash
adb logcat | grep -E "Potato|org.pt.msg"
```

#### 查看服务状态：
```bash
adb logcat | grep -E "SERVICE_CRITICAL|服务启动|服务停止"
```

#### 查看上传状态：
```bash
adb logcat | grep -E "UPLOAD_CRITICAL|上传成功|上传失败"
```

### 3. Android Studio过滤

在Android Studio的Logcat窗口中设置过滤器：

#### 过滤器1 - 关键事件：
- **Filter Name**: 关键事件
- **Log Tag**: 留空
- **Log Message**: `CRITICAL|🔥`
- **Regex**: 勾选

#### 过滤器2 - 短信事件：
- **Filter Name**: 短信事件  
- **Log Tag**: 留空
- **Log Message**: `SMS_CRITICAL|短信|验证码`
- **Regex**: 勾选

#### 过滤器3 - Potato过滤：
- **Filter Name**: Potato过滤
- **Log Tag**: 留空
- **Log Message**: `Potato|org.pt.msg`
- **Regex**: 勾选

### 4. 临时调整日志级别

如果需要更多或更少的日志信息，可以在代码中临时调整：

#### 只看最关键的信息：
```kotlin
LogLevel.setLevel(LogLevel.Level.CRITICAL)
```

#### 看更多调试信息：
```kotlin
LogLevel.setLevel(LogLevel.Level.DEBUG)
```

#### 完全关闭日志：
```kotlin
LogLevel.setLevel(LogLevel.Level.NONE)
```

### 5. 常见问题排查

#### 问题：短信拦截不工作
**查看命令：**
```bash
adb logcat | grep -E "SMS_CRITICAL|短信|NotificationAccessHelper"
```

#### 问题：Potato通知被误拦截
**查看命令：**
```bash
adb logcat | grep -E "Potato|org.pt.msg|过滤"
```

#### 问题：服务被杀死
**查看命令：**
```bash
adb logcat | grep -E "SERVICE_CRITICAL|KeepAlive|服务"
```

#### 问题：上传失败
**查看命令：**
```bash
adb logcat | grep -E "UPLOAD_CRITICAL|Qiniu|上传"
```

### 6. 实时监控脚本

创建一个实时监控关键事件的脚本：

```bash
# 创建monitor.sh
#!/bin/bash
echo "开始监控关键事件..."
adb logcat -c  # 清除旧日志
adb logcat | grep --line-buffered -E "CRITICAL|🔥" | while read line; do
    echo "[$(date '+%H:%M:%S')] $line"
done
```

### 7. 日志保存

#### 保存关键日志到文件：
```bash
adb logcat | grep -E "CRITICAL|🔥" > critical_logs.txt
```

#### 保存特定时间段的日志：
```bash
# 保存最近1000行关键日志
adb logcat -t 1000 | grep -E "CRITICAL|🔥" > recent_critical.txt
```

### 8. 性能优化建议

1. **使用过滤脚本**：避免查看所有日志，只关注需要的部分
2. **定期清理**：使用 `adb logcat -c` 清除日志缓存
3. **调整级别**：根据需要动态调整日志级别
4. **分类查看**：按模块分别查看，避免信息混乱

### 9. 快速命令参考

```bash
# 清除日志
adb logcat -c

# 查看最近100行
adb logcat -t 100

# 只看错误级别
adb logcat *:E

# 保存到文件
adb logcat > full_log.txt

# 实时过滤关键信息
adb logcat | grep -E "CRITICAL|🔥|短信|Potato"
```

这样设置后，你就可以很容易地捕捉到有效信息，而不会被大量的调试日志干扰。
